# 🏢 Système de Comptabilité pour Entreprise de Carburants

## 📋 Présentation du Projet

### 🎯 Objectif
Développer un système de comptabilité complet et conforme à la réglementation marocaine pour une entreprise spécialisée dans la vente de carburants.

### 🏗️ Architecture Technique
- **Backend** : Python 3.11+ avec Flask
- **Base de données** : SQLite (locale, aucune configuration requise)
- **Frontend** : HTML5, CSS3, JavaScript, Bootstrap 5
- **Structure** : MVC (Model-View-Controller)

---

## ✨ Fonctionnalités Implémentées

### 📊 Gestion des Entités
✅ **Clients** : CRUD complet avec suivi des soldes  
✅ **Fournisseurs** : Gestion des achats et dettes  
✅ **Produits** : Carburants avec prix d'achat/vente et stocks  
✅ **Employés** : Gestion complète de la paie  

### 💰 Transactions Commerciales
✅ **Achats** : Enregistrement avec mise à jour automatique des stocks  
✅ **Ventes** : Calcul automatique des soldes clients  
✅ **Historique** : Suivi complet de toutes les opérations  

### 🧮 Calculs Fiscaux Automatiques
✅ **Salaires** : CNSS (4.48%), AMO (2.26%), IR (barème progressif)  
✅ **TVA** : Calcul automatique collectée/déductible (20%)  
✅ **Conformité** : Réglementation marocaine 2024  

### 📈 Rapports et Analyses
✅ **Rapport mensuel** : CA, TVA, masse salariale, créances/dettes  
✅ **Bilan annuel** : Résultats financiers et ratios  
✅ **État des stocks** : Alertes stock faible  
✅ **Créances/Dettes** : Suivi des impayés  

### 🔧 Déclarations Fiscales
✅ **TVA mensuelle** : Génération automatique  
✅ **IR mensuel** : Déclaration employés  
✅ **IS annuel** : Impôt sur les sociétés  

---

## 🎨 Interface Utilisateur

### 📱 Design Moderne
- Interface responsive (mobile/desktop)
- Navigation intuitive avec menu latéral
- Tableaux interactifs avec recherche/tri
- Modals pour ajout/modification
- Alertes visuelles et notifications

### 🎯 Expérience Utilisateur
- Actions rapides depuis le tableau de bord
- Calculs en temps réel
- Validation des formulaires
- Confirmations de suppression
- Raccourcis clavier

---

## 📁 Structure du Code

```
COMPTA 2/
├── 📄 main.py                    # Application Flask principale
├── 🚀 run.py                     # Script de démarrage optimisé
├── 🔧 init_data.py               # Initialisation données d'exemple
├── ✅ verifier_systeme.py        # Vérification complète du système
├── 📋 requirements.txt           # Dépendances Python
├── 📚 README.md                  # Documentation complète
├── ⚡ DEMARRAGE_RAPIDE.md        # Guide de démarrage
├── 🔬 GUIDE_TECHNIQUE.md         # Documentation technique
├── 📂 models/                    # Modèles de données (7 fichiers)
├── 🛠️ utils/                     # Utilitaires calculs/rapports
├── 🎨 templates/                 # Templates HTML (8 fichiers)
├── 💎 static/                    # CSS/JS personnalisés
└── 🗄️ data/                      # Base de données SQLite
```

---

## 🧮 Calculs Fiscaux Implémentés

### 💼 Salaires (Réglementation Marocaine)
```
Salaire Brut: 8000 DH
├── CNSS (4.48%): 268.80 DH (plafonné à 6000 DH)
├── AMO (2.26%): 135.60 DH (plafonné à 6000 DH)
├── IR (barème progressif): Variable selon tranches
└── Salaire Net: Brut - CNSS - AMO - IR
```

### 🧾 TVA
```
Ventes HT: 100,000 DH
├── TVA Collectée (20%): 20,000 DH
Achats HT: 60,000 DH
├── TVA Déductible (20%): 12,000 DH
└── TVA Due: 8,000 DH
```

---

## 📊 Tableaux de Bord

### 🏠 Tableau Principal
- Statistiques en temps réel
- CA du mois courant
- Nombre d'entités par type
- Transactions récentes
- Actions rapides

### 📈 Rapports Mensuels
- Chiffre d'affaires et marge brute
- TVA collectée/déductible/due
- Masse salariale détaillée
- Créances clients et dettes fournisseurs

### 📋 Bilan Annuel
- Résultats financiers complets
- Ratios de performance (marge brute/nette)
- Impôts payés par type
- Évolution annuelle

---

## 🔒 Sécurité et Fiabilité

### 🛡️ Validation des Données
- Contrôles de saisie stricts
- Validation des montants et quantités
- Vérification des stocks avant vente
- Protection contre les doublons

### 💾 Intégrité des Données
- Transactions atomiques
- Mise à jour automatique des soldes
- Cohérence stocks/transactions
- Sauvegarde automatique SQLite

### 🔧 Robustesse
- Gestion d'erreurs complète
- Messages d'erreur explicites
- Logs détaillés
- Tests de vérification intégrés

---

## 🚀 Performance et Optimisation

### ⚡ Rapidité
- Base SQLite optimisée
- Index sur les requêtes fréquentes
- Calculs en temps réel
- Interface responsive

### 📱 Compatibilité
- Navigateurs modernes
- Responsive design
- Mobile-friendly
- Cross-platform (Windows/Mac/Linux)

---

## 🎯 Conformité Réglementaire

### 🇲🇦 Maroc 2024
✅ Barème IR actualisé  
✅ Taux CNSS/AMO officiels  
✅ TVA standard 20%  
✅ Calculs conformes DGI  

### 📋 Déclarations
✅ Format déclarations mensuelles  
✅ Calculs IS annuel  
✅ Rapports conformes  

---

## 🎉 Points Forts du Système

### 🏆 Avantages Techniques
- **Simplicité** : Installation en 3 commandes
- **Autonomie** : Aucune dépendance externe
- **Fiabilité** : Tests automatisés intégrés
- **Évolutivité** : Architecture modulaire

### 💼 Avantages Métier
- **Conformité** : Réglementation marocaine
- **Automatisation** : Calculs fiscaux automatiques
- **Traçabilité** : Historique complet
- **Reporting** : Rapports détaillés

### 👥 Avantages Utilisateur
- **Simplicité** : Interface intuitive
- **Rapidité** : Actions en un clic
- **Mobilité** : Accès depuis tout navigateur
- **Sécurité** : Données locales protégées

---

## 📈 Résultats Obtenus

### ✅ Objectifs Atteints
🎯 **Système complet** : Toutes les fonctionnalités demandées  
🎯 **Interface web** : Application accessible par navigateur  
🎯 **Calculs automatiques** : Conformes à la réglementation  
🎯 **Base SQLite** : Données sauvegardées localement  
🎯 **Structure modulaire** : Code organisé et maintenable  

### 📊 Métriques du Projet
- **25 fichiers** créés
- **7 modèles** de données
- **8 templates** HTML
- **15+ routes** Flask
- **6 tests** de vérification automatiques

---

## 🔮 Évolutions Possibles

### 🚀 Extensions Futures
- API REST pour intégrations
- Export PDF des rapports
- Notifications email automatiques
- Sauvegarde cloud
- Multi-utilisateurs avec authentification
- Tableau de bord avancé avec graphiques

### 📱 Améliorations UX
- Application mobile native
- Mode hors-ligne
- Thèmes personnalisables
- Raccourcis clavier avancés

---

## 🏁 Conclusion

Le système de comptabilité développé répond parfaitement aux exigences d'une entreprise marocaine de carburants. Il offre une solution complète, moderne et conforme à la réglementation, avec une interface intuitive et des calculs automatisés fiables.

**🎉 Prêt pour la production et l'utilisation immédiate !**

---

*Projet réalisé avec Python, Flask et SQLite*  
*Conforme à la réglementation fiscale marocaine 2024* 🇲🇦
