"""
Modèle Employé pour la gestion des employés et calculs de paie
"""

from models.database import db
from utils.calculs import calculer_salaire

class Employe:
    def __init__(self, nom, cin, numero_cnss, salaire_brut):
        self.nom = nom
        self.cin = cin
        self.numero_cnss = numero_cnss
        self.salaire_brut = salaire_brut
        
        # Calculer automatiquement les déductions et le salaire net
        calculs = calculer_salaire(salaire_brut)
        self.salaire_net = calculs['salaire_net']
        self.cnss = calculs['cnss']
        self.amo = calculs['amo']
        self.ir = calculs['ir']
    
    def save(self):
        """Sauvegarder l'employé en base de données"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO employes (nom, cin, numero_cnss, salaire_brut, salaire_net, cnss, amo, ir)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (self.nom, self.cin, self.numero_cnss, self.salaire_brut, 
              self.salaire_net, self.cnss, self.amo, self.ir))
        
        employe_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return employe_id
    
    @staticmethod
    def get_all():
        """Récupérer tous les employés"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM employes ORDER BY nom')
        employes = cursor.fetchall()
        conn.close()
        
        return employes
    
    @staticmethod
    def get_by_id(employe_id):
        """Récupérer un employé par son ID"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM employes WHERE id = ?', (employe_id,))
        employe = cursor.fetchone()
        conn.close()
        
        return employe
    
    @staticmethod
    def get_by_cin(cin):
        """Récupérer un employé par son CIN"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM employes WHERE cin = ?', (cin,))
        employe = cursor.fetchone()
        conn.close()
        
        return employe
    
    @staticmethod
    def update(employe_id, nom, cin, numero_cnss, salaire_brut):
        """Mettre à jour un employé et recalculer ses déductions"""
        calculs = calculer_salaire(salaire_brut)
        
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE employes 
            SET nom = ?, cin = ?, numero_cnss = ?, salaire_brut = ?,
                salaire_net = ?, cnss = ?, amo = ?, ir = ?
            WHERE id = ?
        ''', (nom, cin, numero_cnss, salaire_brut, 
              calculs['salaire_net'], calculs['cnss'], calculs['amo'], calculs['ir'],
              employe_id))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def delete(employe_id):
        """Supprimer un employé"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM employes WHERE id = ?', (employe_id,))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def calculer_masse_salariale():
        """Calculer la masse salariale totale"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                SUM(salaire_brut) as total_brut,
                SUM(salaire_net) as total_net,
                SUM(cnss) as total_cnss,
                SUM(amo) as total_amo,
                SUM(ir) as total_ir
            FROM employes
        ''')
        
        result = cursor.fetchone()
        conn.close()
        
        return {
            'total_brut': result[0] if result[0] else 0.0,
            'total_net': result[1] if result[1] else 0.0,
            'total_cnss': result[2] if result[2] else 0.0,
            'total_amo': result[3] if result[3] else 0.0,
            'total_ir': result[4] if result[4] else 0.0
        }
