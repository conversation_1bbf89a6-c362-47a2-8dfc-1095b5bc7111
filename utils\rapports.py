"""
Fonctions de génération de rapports pour le système de comptabilité
"""

from models.transaction import Transaction
from models.client import Client
from models.fournisseur import Fournisseur
from models.employe import Employe
from models.declaration_fiscale import DeclarationFiscale
from models.declaration_sociale import DeclarationSociale
from utils.calculs import calculer_tva_collectee, calculer_tva_deductible
from datetime import datetime

def generer_rapport_mensuel(mois, annee):
    """
    Générer un rapport mensuel complet
    
    Args:
        mois (int): Mo<PERSON> (1-12)
        annee (int): Année
    
    Returns:
        dict: Rapport mensuel complet
    """
    
    # Récupérer les transactions du mois
    ventes = Transaction.get_ventes_mensuelles(mois, annee)
    achats = Transaction.get_achats_mensuels(mois, annee)
    
    # Calculer le chiffre d'affaires
    ca_mensuel = Transaction.calculer_ca_mensuel(mois, annee)
    
    # Calculer les totaux d'achats
    total_achats = sum([achat[7] for achat in achats])  # total de chaque transaction
    
    # Calculer la TVA
    tva_collectee = ca_mensuel * 0.20  # 20% de TVA sur les ventes
    tva_deductible = total_achats * 0.20  # 20% de TVA sur les achats
    tva_due = tva_collectee - tva_deductible
    
    # Masse salariale
    masse_salariale = Employe.calculer_masse_salariale()
    
    # Déclarations sociales du mois
    periode = f"{annee}-{mois:02d}"
    totaux_sociaux = DeclarationSociale.calculer_totaux_mensuels(periode)
    
    # Soldes clients et fournisseurs
    clients_creances = Client.get_soldes_positifs()
    fournisseurs_dettes = Fournisseur.get_soldes_negatifs()
    
    rapport = {
        'periode': f"{mois:02d}/{annee}",
        'chiffre_affaires': {
            'ca_mensuel': ca_mensuel,
            'nombre_ventes': len(ventes),
            'total_achats': total_achats,
            'nombre_achats': len(achats),
            'marge_brute': ca_mensuel - total_achats
        },
        'tva': {
            'tva_collectee': tva_collectee,
            'tva_deductible': tva_deductible,
            'tva_due': tva_due
        },
        'masse_salariale': masse_salariale,
        'cotisations_sociales': totaux_sociaux,
        'creances_clients': {
            'nombre': len(clients_creances),
            'total': sum([client[4] for client in clients_creances])
        },
        'dettes_fournisseurs': {
            'nombre': len(fournisseurs_dettes),
            'total': abs(sum([fournisseur[4] for fournisseur in fournisseurs_dettes]))
        }
    }
    
    return rapport

def generer_bilan_annuel(annee):
    """
    Générer un bilan annuel
    
    Args:
        annee (int): Année
    
    Returns:
        dict: Bilan annuel
    """
    
    # Calculer le CA annuel
    ca_annuel = 0
    total_achats_annuel = 0
    
    for mois in range(1, 13):
        ca_mensuel = Transaction.calculer_ca_mensuel(mois, annee)
        achats_mensuels = Transaction.get_achats_mensuels(mois, annee)
        total_achats_mensuel = sum([achat[7] for achat in achats_mensuels])
        
        ca_annuel += ca_mensuel
        total_achats_annuel += total_achats_mensuel
    
    # Résultat brut
    resultat_brut = ca_annuel - total_achats_annuel
    
    # Charges salariales annuelles
    masse_salariale = Employe.calculer_masse_salariale()
    charges_salariales_annuelles = masse_salariale['total_brut'] * 12
    
    # Résultat avant impôts
    resultat_avant_impots = resultat_brut - charges_salariales_annuelles
    
    # Impôts payés dans l'année
    impots_annee = DeclarationFiscale.get_total_impots_annee(annee)
    
    # Résultat net
    total_impots = sum(impots_annee.values())
    resultat_net = resultat_avant_impots - total_impots
    
    bilan = {
        'annee': annee,
        'chiffre_affaires_annuel': ca_annuel,
        'total_achats_annuel': total_achats_annuel,
        'resultat_brut': resultat_brut,
        'charges_salariales': charges_salariales_annuelles,
        'resultat_avant_impots': resultat_avant_impots,
        'impots_payes': impots_annee,
        'total_impots': total_impots,
        'resultat_net': resultat_net,
        'marge_brute_pct': (resultat_brut / ca_annuel * 100) if ca_annuel > 0 else 0,
        'marge_nette_pct': (resultat_net / ca_annuel * 100) if ca_annuel > 0 else 0
    }
    
    return bilan

def generer_rapport_stocks():
    """Générer un rapport sur l'état des stocks"""
    from models.produit import Produit
    
    produits = Produit.get_all()
    stocks_faibles = Produit.get_stock_faible(100)  # Seuil de 100 litres
    
    valeur_stock_total = 0
    for produit in produits:
        valeur_stock_total += produit[4] * produit[2]  # quantité * prix_achat
    
    rapport_stocks = {
        'nombre_produits': len(produits),
        'valeur_stock_total': valeur_stock_total,
        'stocks_faibles': {
            'nombre': len(stocks_faibles),
            'produits': stocks_faibles
        },
        'detail_stocks': produits
    }
    
    return rapport_stocks

def generer_rapport_clients():
    """Générer un rapport sur les clients"""
    clients = Client.get_all()
    clients_creances = Client.get_soldes_positifs()
    
    total_creances = sum([client[4] for client in clients_creances])
    
    rapport_clients = {
        'nombre_clients_total': len(clients),
        'nombre_clients_creances': len(clients_creances),
        'total_creances': total_creances,
        'clients_avec_creances': clients_creances,
        'tous_clients': clients
    }
    
    return rapport_clients

def generer_rapport_fournisseurs():
    """Générer un rapport sur les fournisseurs"""
    fournisseurs = Fournisseur.get_all()
    fournisseurs_dettes = Fournisseur.get_soldes_negatifs()
    
    total_dettes = abs(sum([fournisseur[4] for fournisseur in fournisseurs_dettes]))
    
    rapport_fournisseurs = {
        'nombre_fournisseurs_total': len(fournisseurs),
        'nombre_fournisseurs_dettes': len(fournisseurs_dettes),
        'total_dettes': total_dettes,
        'fournisseurs_avec_dettes': fournisseurs_dettes,
        'tous_fournisseurs': fournisseurs
    }
    
    return rapport_fournisseurs
