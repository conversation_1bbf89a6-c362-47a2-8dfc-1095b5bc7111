{% extends "base.html" %}

{% block title %}Gestion des Transactions - Système de Comptabilité{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-exchange-alt"></i> Gestion des Transactions</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterTransactionModal">
            <i class="fas fa-plus"></i> Nouvelle Transaction
        </button>
    </div>
</div>

<!-- Liste des transactions -->
<div class="card shadow">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold"><i class="fas fa-list"></i> Historique des Transactions</h6>
    </div>
    <div class="card-body">
        {% if transactions %}
        <div class="table-responsive">
            <table class="table table-bordered" id="transactionsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Date</th>
                        <th>Type</th>
                        <th>Entité</th>
                        <th>Produit</th>
                        <th>Quantité</th>
                        <th>Prix Unit.</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction[0] }}</td>
                        <td>{{ transaction[8][:16] if transaction[8] else 'N/A' }}</td>
                        <td>
                            <span class="badge bg-{{ 'success' if transaction[1] == 'vente' else 'primary' }}">
                                {{ transaction[1].title() }}
                            </span>
                        </td>
                        <td>{{ transaction[2].title() }} #{{ transaction[3] }}</td>
                        <td>{{ transaction[9] if transaction|length > 9 else 'ID: ' + transaction[4]|string }}</td>
                        <td>{{ "{:,.2f}".format(transaction[5]) }}</td>
                        <td>{{ "{:,.2f}".format(transaction[6]) }} DH</td>
                        <td>{{ "{:,.2f}".format(transaction[7]) }} DH</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-muted">Aucune transaction enregistrée.</p>
        {% endif %}
    </div>
</div>

<!-- Modal Ajouter Transaction -->
<div class="modal fade" id="ajouterTransactionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Nouvelle Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('ajouter_transaction') }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Type de Transaction *</label>
                                <select class="form-select" id="type" name="type" required onchange="updateEntiteOptions()">
                                    <option value="">Sélectionner...</option>
                                    <option value="achat">Achat</option>
                                    <option value="vente">Vente</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type_entite" class="form-label">Type d'Entité *</label>
                                <select class="form-select" id="type_entite" name="type_entite" required onchange="updateEntiteList()">
                                    <option value="">Sélectionner...</option>
                                    <option value="client">Client</option>
                                    <option value="fournisseur">Fournisseur</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="entite_id" class="form-label">Entité *</label>
                                <select class="form-select" id="entite_id" name="entite_id" required>
                                    <option value="">Sélectionner d'abord le type...</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="produit_id" class="form-label">Produit *</label>
                                <select class="form-select" id="produit_id" name="produit_id" required onchange="updatePrixSuggere()">
                                    <option value="">Sélectionner...</option>
                                    {% for produit in produits %}
                                    <option value="{{ produit[0] }}" 
                                            data-prix-achat="{{ produit[2] }}" 
                                            data-prix-vente="{{ produit[3] }}"
                                            data-stock="{{ produit[4] }}">
                                        {{ produit[1] }} (Stock: {{ "{:,.0f}".format(produit[4]) }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="quantite" class="form-label">Quantité *</label>
                                <input type="number" step="0.01" class="form-control" id="quantite" name="quantite" required onchange="calculerTotal()">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="prix_unitaire" class="form-label">Prix Unitaire (DH) *</label>
                                <input type="number" step="0.01" class="form-control" id="prix_unitaire" name="prix_unitaire" required onchange="calculerTotal()">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="total" class="form-label">Total (DH)</label>
                                <input type="number" step="0.01" class="form-control" id="total" name="total" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info" id="stockAlert" style="display: none;">
                        <i class="fas fa-info-circle"></i> <span id="stockMessage"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<script>
// Données pour les entités
const clients = {{ clients|tojson }};
const fournisseurs = {{ fournisseurs|tojson }};

function updateEntiteOptions() {
    const type = document.getElementById('type').value;
    const typeEntiteSelect = document.getElementById('type_entite');
    
    // Réinitialiser les options
    typeEntiteSelect.innerHTML = '<option value="">Sélectionner...</option>';
    
    if (type === 'achat') {
        typeEntiteSelect.innerHTML += '<option value="fournisseur">Fournisseur</option>';
    } else if (type === 'vente') {
        typeEntiteSelect.innerHTML += '<option value="client">Client</option>';
    } else {
        typeEntiteSelect.innerHTML += '<option value="client">Client</option>';
        typeEntiteSelect.innerHTML += '<option value="fournisseur">Fournisseur</option>';
    }
    
    // Réinitialiser la liste des entités
    updateEntiteList();
}

function updateEntiteList() {
    const typeEntite = document.getElementById('type_entite').value;
    const entiteSelect = document.getElementById('entite_id');
    
    entiteSelect.innerHTML = '<option value="">Sélectionner...</option>';
    
    if (typeEntite === 'client') {
        clients.forEach(client => {
            entiteSelect.innerHTML += `<option value="${client[0]}">${client[1]}</option>`;
        });
    } else if (typeEntite === 'fournisseur') {
        fournisseurs.forEach(fournisseur => {
            entiteSelect.innerHTML += `<option value="${fournisseur[0]}">${fournisseur[1]}</option>`;
        });
    }
}

function updatePrixSuggere() {
    const produitSelect = document.getElementById('produit_id');
    const selectedOption = produitSelect.options[produitSelect.selectedIndex];
    const type = document.getElementById('type').value;
    const prixUnitaireInput = document.getElementById('prix_unitaire');
    
    if (selectedOption.value) {
        const prixAchat = parseFloat(selectedOption.dataset.prixAchat);
        const prixVente = parseFloat(selectedOption.dataset.prixVente);
        const stock = parseFloat(selectedOption.dataset.stock);
        
        // Suggérer le prix selon le type de transaction
        if (type === 'achat') {
            prixUnitaireInput.value = prixAchat.toFixed(2);
        } else if (type === 'vente') {
            prixUnitaireInput.value = prixVente.toFixed(2);
            
            // Afficher alerte si stock faible
            if (stock < 100) {
                document.getElementById('stockMessage').textContent = `Attention: Stock faible (${stock.toFixed(0)} unités restantes)`;
                document.getElementById('stockAlert').style.display = 'block';
            } else {
                document.getElementById('stockAlert').style.display = 'none';
            }
        }
        
        calculerTotal();
    }
}

function calculerTotal() {
    const quantite = parseFloat(document.getElementById('quantite').value) || 0;
    const prixUnitaire = parseFloat(document.getElementById('prix_unitaire').value) || 0;
    const total = quantite * prixUnitaire;
    
    document.getElementById('total').value = total.toFixed(2);
}

// DataTable
$(document).ready(function() {
    $('#transactionsTable').DataTable({
        "language": {
            "search": "Rechercher:",
            "lengthMenu": "Afficher _MENU_ entrées",
            "info": "Affichage de _START_ à _END_ sur _TOTAL_ entrées",
            "paginate": {
                "first": "Premier",
                "last": "Dernier",
                "next": "Suivant",
                "previous": "Précédent"
            }
        },
        "pageLength": 25,
        "order": [[ 1, "desc" ]]
    });
});
</script>
{% endblock %}
