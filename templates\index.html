{% extends "base.html" %}

{% block title %}Tableau de bord - Système de Comptabilité{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tachometer-alt"></i> Tableau de bord</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-calendar"></i> {{ "Aujourd'hui: " + moment().format('DD/MM/YYYY') if moment else "Aujourd'hui" }}
            </button>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Clients</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.nb_clients }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">CA Mois Courant</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "{:,.2f}".format(stats.ca_mois_courant) }} DH</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Produits</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.nb_produits }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-oil-can fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Employés</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.nb_employes }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transactions récentes -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold"><i class="fas fa-list"></i> Transactions Récentes</h6>
            </div>
            <div class="card-body">
                {% if stats.transactions_recentes %}
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Produit</th>
                                <th>Quantité</th>
                                <th>Prix Unitaire</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in stats.transactions_recentes %}
                            <tr>
                                <td>{{ transaction[8][:10] if transaction[8] else 'N/A' }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if transaction[1] == 'vente' else 'primary' }}">
                                        {{ transaction[1].title() }}
                                    </span>
                                </td>
                                <td>{{ transaction[9] if transaction|length > 9 else 'Produit ID: ' + transaction[4]|string }}</td>
                                <td>{{ "{:,.2f}".format(transaction[5]) }}</td>
                                <td>{{ "{:,.2f}".format(transaction[6]) }} DH</td>
                                <td>{{ "{:,.2f}".format(transaction[7]) }} DH</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">Aucune transaction enregistrée.</p>
                {% endif %}
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('transactions') }}" class="btn btn-primary">
                        <i class="fas fa-eye"></i> Voir toutes les transactions
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold"><i class="fas fa-bolt"></i> Actions Rapides</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('transactions') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-plus"></i><br>
                            Nouvelle Transaction
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('clients') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-user-plus"></i><br>
                            Ajouter Client
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('produits') }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-box"></i><br>
                            Gérer Stock
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('rapports') }}" class="btn btn-secondary btn-lg w-100">
                            <i class="fas fa-chart-line"></i><br>
                            Voir Rapports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
