"""
Modèle Déclaration Sociale pour la gestion des déclarations CNSS/AMO
"""

from models.database import db
from models.employe import Employe

class DeclarationSociale:
    def __init__(self, employe_id, mois, cnss, amo, ir):
        self.employe_id = employe_id
        self.mois = mois  # Format: 'YYYY-MM'
        self.cnss = cnss
        self.amo = amo
        self.ir = ir
        self.total = cnss + amo + ir
    
    def save(self):
        """Sauvegarder la déclaration sociale"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO declarations_sociales (employe_id, mois, cnss, amo, ir, total)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (self.employe_id, self.mois, self.cnss, self.amo, self.ir, self.total))
        
        declaration_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return declaration_id
    
    @staticmethod
    def get_all():
        """Récupérer toutes les déclarations sociales"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ds.*, e.nom as employe_nom
            FROM declarations_sociales ds
            JOIN employes e ON ds.employe_id = e.id
            ORDER BY ds.mois DESC, e.nom
        ''')
        declarations = cursor.fetchall()
        conn.close()
        
        return declarations
    
    @staticmethod
    def get_by_mois(mois):
        """Récupérer les déclarations sociales par mois"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ds.*, e.nom as employe_nom
            FROM declarations_sociales ds
            JOIN employes e ON ds.employe_id = e.id
            WHERE ds.mois = ?
            ORDER BY e.nom
        ''', (mois,))
        declarations = cursor.fetchall()
        conn.close()
        
        return declarations
    
    @staticmethod
    def get_by_employe(employe_id):
        """Récupérer les déclarations sociales d'un employé"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM declarations_sociales 
            WHERE employe_id = ? 
            ORDER BY mois DESC
        ''', (employe_id,))
        declarations = cursor.fetchall()
        conn.close()
        
        return declarations
    
    @staticmethod
    def generer_declarations_mensuelles(mois, annee):
        """Générer les déclarations sociales pour tous les employés du mois"""
        periode = f"{annee}-{mois:02d}"
        employes = Employe.get_all()
        declarations_creees = []
        
        for employe in employes:
            # Vérifier si la déclaration existe déjà
            existing = DeclarationSociale.get_by_employe_et_mois(employe[0], periode)
            if not existing:
                declaration = DeclarationSociale(
                    employe_id=employe[0],
                    mois=periode,
                    cnss=employe[6],  # cnss de l'employé
                    amo=employe[7],   # amo de l'employé
                    ir=employe[8]     # ir de l'employé
                )
                declaration_id = declaration.save()
                declarations_creees.append(declaration_id)
        
        return declarations_creees
    
    @staticmethod
    def get_by_employe_et_mois(employe_id, mois):
        """Vérifier si une déclaration existe pour un employé et un mois"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM declarations_sociales 
            WHERE employe_id = ? AND mois = ?
        ''', (employe_id, mois))
        declaration = cursor.fetchone()
        conn.close()
        
        return declaration
    
    @staticmethod
    def calculer_totaux_mensuels(mois):
        """Calculer les totaux des cotisations sociales pour un mois"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                SUM(cnss) as total_cnss,
                SUM(amo) as total_amo,
                SUM(ir) as total_ir,
                SUM(total) as total_general
            FROM declarations_sociales 
            WHERE mois = ?
        ''', (mois,))
        
        result = cursor.fetchone()
        conn.close()
        
        return {
            'total_cnss': result[0] if result[0] else 0.0,
            'total_amo': result[1] if result[1] else 0.0,
            'total_ir': result[2] if result[2] else 0.0,
            'total_general': result[3] if result[3] else 0.0
        }
    
    @staticmethod
    def delete(declaration_id):
        """Supprimer une déclaration sociale"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM declarations_sociales WHERE id = ?', (declaration_id,))
        
        conn.commit()
        conn.close()
