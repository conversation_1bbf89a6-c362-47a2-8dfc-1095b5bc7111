"""
Modèle Fournisseur pour la gestion des fournisseurs de l'entreprise de carburants
"""

from models.database import db

class Fournisseur:
    def __init__(self, nom, telephone=None, adresse=None, solde=0.0):
        self.nom = nom
        self.telephone = telephone
        self.adresse = adresse
        self.solde = solde
    
    def save(self):
        """Sauvegarder le fournisseur en base de données"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO fournisseurs (nom, telephone, adresse, solde)
            VALUES (?, ?, ?, ?)
        ''', (self.nom, self.telephone, self.adresse, self.solde))
        
        fournisseur_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return fournisseur_id
    
    @staticmethod
    def get_all():
        """Récupérer tous les fournisseurs"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM fournisseurs ORDER BY nom')
        fournisseurs = cursor.fetchall()
        conn.close()
        
        return fournisseurs
    
    @staticmethod
    def get_by_id(fournisseur_id):
        """Récupérer un fournisseur par son ID"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM fournisseurs WHERE id = ?', (fournisseur_id,))
        fournisseur = cursor.fetchone()
        conn.close()
        
        return fournisseur
    
    @staticmethod
    def update(fournisseur_id, nom, telephone=None, adresse=None, solde=None):
        """Mettre à jour un fournisseur"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE fournisseurs 
            SET nom = ?, telephone = ?, adresse = ?, solde = ?
            WHERE id = ?
        ''', (nom, telephone, adresse, solde, fournisseur_id))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def update_solde(fournisseur_id, nouveau_solde):
        """Mettre à jour le solde d'un fournisseur"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('UPDATE fournisseurs SET solde = ? WHERE id = ?', (nouveau_solde, fournisseur_id))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def delete(fournisseur_id):
        """Supprimer un fournisseur"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM fournisseurs WHERE id = ?', (fournisseur_id,))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def get_soldes_negatifs():
        """Récupérer les fournisseurs avec un solde négatif (dettes)"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM fournisseurs WHERE solde < 0 ORDER BY solde ASC')
        fournisseurs = cursor.fetchall()
        conn.close()
        
        return fournisseurs
