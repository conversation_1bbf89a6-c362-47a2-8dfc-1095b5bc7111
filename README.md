# Système de Comptabilité pour Entreprise de Carburants

## 🏢 Description

Système de comptabilité complet développé en Python avec Flask pour une entreprise marocaine spécialisée dans la vente de carburants. Le système gère tous les aspects comptables selon la réglementation marocaine.

## ✨ Fonctionnalités

### 📊 Gestion des Entités
- **Clients** : Gestion complète avec suivi des soldes
- **Fournisseurs** : Gestion des achats et des dettes
- **Produits** : Gestion des carburants avec prix d'achat/vente et stocks
- **Employés** : Gestion de la paie avec calculs automatiques (CNSS, AMO, IR)

### 💰 Transactions
- **Achats** : Enregistrement des achats avec mise à jour automatique des stocks
- **Ventes** : Enregistrement des ventes avec calcul automatique des soldes clients
- **Historique** : Suivi complet de toutes les transactions

### 📋 Déclarations Fiscales
- **TVA mensuelle** : Calcul automatique de la TVA collectée et déductible
- **IR mensuel** : Déclaration de l'impôt sur le revenu des employés
- **IS annuel** : Déclaration de l'impôt sur les sociétés

### 📈 Rapports et Analyses
- **Rapport mensuel** : CA, TVA, masse salariale, créances/dettes
- **Bilan annuel** : Résultats financiers et ratios de performance
- **État des stocks** : Suivi des niveaux de stock avec alertes
- **Créances clients** : Liste des impayés
- **Dettes fournisseurs** : Suivi des montants dus

## 🛠️ Technologies Utilisées

- **Backend** : Python 3.11+, Flask
- **Base de données** : SQLite
- **Frontend** : HTML5, CSS3, JavaScript, Bootstrap 5
- **Calculs** : Conformes à la réglementation fiscale marocaine 2024

## 📁 Structure du Projet

```
COMPTA 2/
├── main.py                    # Application Flask principale
├── run.py                     # Script de démarrage
├── requirements.txt           # Dépendances Python
├── models/                    # Modèles de données
│   ├── __init__.py
│   ├── database.py           # Configuration base de données
│   ├── client.py             # Modèle Client
│   ├── fournisseur.py        # Modèle Fournisseur
│   ├── produit.py            # Modèle Produit
│   ├── transaction.py        # Modèle Transaction
│   ├── employe.py            # Modèle Employé
│   ├── declaration_fiscale.py # Déclarations fiscales
│   └── declaration_sociale.py # Déclarations sociales
├── utils/                     # Utilitaires
│   ├── __init__.py
│   ├── calculs.py            # Calculs salaires et impôts
│   └── rapports.py           # Génération de rapports
├── templates/                 # Templates HTML
│   ├── base.html
│   ├── index.html
│   ├── clients.html
│   ├── fournisseurs.html
│   ├── produits.html
│   ├── transactions.html
│   ├── employes.html
│   └── rapports.html
├── static/                    # Fichiers statiques
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── main.js
└── data/                      # Base de données
    └── fuel_company.db       # Base SQLite (créée automatiquement)
```

## 🚀 Installation et Démarrage

### Prérequis
- Python 3.11 ou plus récent
- pip (gestionnaire de paquets Python)

### Installation

1. **Cloner ou télécharger le projet**
   ```bash
   cd "COMPTA 2"
   ```

2. **Installer les dépendances**
   ```bash
   pip install -r requirements.txt
   ```

3. **Démarrer l'application**
   ```bash
   python run.py
   ```
   
   Ou directement :
   ```bash
   python main.py
   ```

4. **Accéder à l'application**
   - Ouvrez votre navigateur
   - Accédez à : `http://localhost:5000`

## 💼 Utilisation

### Premier Démarrage

1. **Ajouter des produits** (carburants)
   - Aller dans "Produits"
   - Ajouter vos carburants avec prix d'achat et de vente

2. **Ajouter des clients et fournisseurs**
   - Enregistrer vos partenaires commerciaux

3. **Ajouter des employés**
   - Saisir les informations avec salaires bruts
   - Les calculs CNSS, AMO, IR se font automatiquement

4. **Enregistrer des transactions**
   - Achats auprès des fournisseurs
   - Ventes aux clients
   - Les stocks et soldes se mettent à jour automatiquement

### Calculs Automatiques

#### Salaires (Réglementation Marocaine 2024)
- **CNSS** : 4.48% (plafonné à 6000 DH)
- **AMO** : 2.26% (plafonné à 6000 DH)
- **IR** : Barème progressif marocain

#### TVA
- **Taux standard** : 20%
- **Calcul automatique** : TVA collectée - TVA déductible

## 📊 Fonctionnalités Avancées

### Rapports Mensuels
- Chiffre d'affaires et marge
- TVA due
- Masse salariale
- Créances et dettes

### Bilan Annuel
- Résultats financiers complets
- Ratios de performance
- Impôts payés par type

### Alertes Automatiques
- Stock faible (< 100 unités)
- Créances importantes
- Dettes en attente

## 🔧 Configuration

### Base de Données
- SQLite intégrée (aucune configuration requise)
- Base créée automatiquement au premier démarrage
- Fichier : `data/fuel_company.db`

### Sécurité
- Changez la clé secrète dans `main.py` :
  ```python
  app.secret_key = 'votre_cle_secrete_unique'
  ```

## 📱 Interface Utilisateur

- **Design responsive** : Compatible mobile et desktop
- **Navigation intuitive** : Menu latéral avec icônes
- **Tableaux interactifs** : Recherche, tri, pagination
- **Modals** : Ajout/modification sans rechargement
- **Alertes visuelles** : Notifications et confirmations

## 🧮 Conformité Fiscale Marocaine

### Barème IR 2024 (Mensuel)
- 0 à 2500 DH : 0%
- 2501 à 4166.67 DH : 10%
- 4166.68 à 5000 DH : 20%
- 5000.01 à 6666.67 DH : 30%
- 6666.68 à 15000 DH : 34%
- Au-delà de 15000 DH : 38%

### Cotisations Sociales
- **CNSS employé** : 4.48% (plafonné)
- **AMO** : 2.26% (plafonné)
- **Plafond** : 6000 DH de salaire brut

## 🔄 Sauvegarde

### Sauvegarde Automatique
- Toutes les données sont sauvegardées automatiquement
- Base SQLite dans `data/fuel_company.db`

### Sauvegarde Manuelle
```bash
# Copier le fichier de base de données
cp data/fuel_company.db backup/fuel_company_$(date +%Y%m%d).db
```

## 🆘 Support et Dépannage

### Problèmes Courants

1. **Erreur de démarrage**
   - Vérifiez que Python 3.11+ est installé
   - Installez Flask : `pip install flask`

2. **Base de données verrouillée**
   - Fermez toutes les instances de l'application
   - Redémarrez

3. **Port déjà utilisé**
   - Changez le port dans `main.py` ou `run.py`

### Logs
- Les erreurs s'affichent dans le terminal
- Mode debug activé par défaut

## 📄 Licence

Ce projet est développé pour un usage professionnel dans le cadre de la gestion comptable d'entreprises marocaines de carburants.

## 🤝 Contribution

Pour toute amélioration ou correction :
1. Identifiez le problème
2. Proposez une solution
3. Testez les modifications
4. Documentez les changements

---

**Développé avec ❤️ pour les entreprises marocaines de carburants**
