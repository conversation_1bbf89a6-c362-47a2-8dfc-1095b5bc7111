"""
Fonctions de calcul pour les salaires, impôts et déclarations
selon la réglementation marocaine
"""

def calculer_salaire(salaire_brut):
    """
    Calculer le salaire net et les déductions selon la réglementation marocaine
    
    Taux appliqués au Maroc (2024):
    - CNSS employé: 4.48% (plafonné à 6000 DH de salaire brut)
    - AMO: 2.26% (plafonné à 6000 DH de salaire brut)
    - IR: Barème progressif
    
    Args:
        salaire_brut (float): Salaire brut mensuel en DH
    
    Returns:
        dict: Dictionnaire contenant les calculs
    """
    
    # Plafond pour CNSS et AMO
    plafond_cnss_amo = 6000.0
    
    # Base de calcul pour CNSS et AMO (plafonnée)
    base_cnss_amo = min(salaire_brut, plafond_cnss_amo)
    
    # Calcul CNSS (4.48%)
    cnss = base_cnss_amo * 0.0448
    
    # Calcul AMO (2.26%)
    amo = base_cnss_amo * 0.0226
    
    # Salaire imposable = Salaire brut - CNSS - AMO
    salaire_imposable = salaire_brut - cnss - amo
    
    # Calcul IR selon le barème progressif marocain
    ir = calculer_ir(salaire_imposable)
    
    # Salaire net = Salaire brut - CNSS - AMO - IR
    salaire_net = salaire_brut - cnss - amo - ir
    
    return {
        'salaire_brut': salaire_brut,
        'salaire_net': salaire_net,
        'cnss': round(cnss, 2),
        'amo': round(amo, 2),
        'ir': round(ir, 2),
        'salaire_imposable': round(salaire_imposable, 2)
    }

def calculer_ir(salaire_imposable):
    """
    Calculer l'impôt sur le revenu selon le barème progressif marocain
    
    Barème IR 2024 (mensuel):
    - Jusqu'à 2500 DH: 0%
    - De 2501 à 4166.67 DH: 10%
    - De 4166.68 à 5000 DH: 20%
    - De 5000.01 à 6666.67 DH: 30%
    - De 6666.68 à 15000 DH: 34%
    - Au-delà de 15000 DH: 38%
    
    Args:
        salaire_imposable (float): Salaire imposable mensuel
    
    Returns:
        float: Montant de l'IR
    """
    
    if salaire_imposable <= 2500:
        return 0.0
    
    ir = 0.0
    
    # Tranche 1: 0 à 2500 DH (0%)
    # Déjà traité ci-dessus
    
    # Tranche 2: 2501 à 4166.67 DH (10%)
    if salaire_imposable > 2500:
        tranche2 = min(salaire_imposable, 4166.67) - 2500
        ir += tranche2 * 0.10
    
    # Tranche 3: 4166.68 à 5000 DH (20%)
    if salaire_imposable > 4166.67:
        tranche3 = min(salaire_imposable, 5000) - 4166.67
        ir += tranche3 * 0.20
    
    # Tranche 4: 5000.01 à 6666.67 DH (30%)
    if salaire_imposable > 5000:
        tranche4 = min(salaire_imposable, 6666.67) - 5000
        ir += tranche4 * 0.30
    
    # Tranche 5: 6666.68 à 15000 DH (34%)
    if salaire_imposable > 6666.67:
        tranche5 = min(salaire_imposable, 15000) - 6666.67
        ir += tranche5 * 0.34
    
    # Tranche 6: Au-delà de 15000 DH (38%)
    if salaire_imposable > 15000:
        tranche6 = salaire_imposable - 15000
        ir += tranche6 * 0.38
    
    return round(ir, 2)

def calculer_tva_collectee(montant_ht, taux_tva=0.20):
    """
    Calculer la TVA collectée sur les ventes
    
    Args:
        montant_ht (float): Montant hors taxes
        taux_tva (float): Taux de TVA (20% par défaut)
    
    Returns:
        dict: Montant HT, TVA et TTC
    """
    tva = montant_ht * taux_tva
    montant_ttc = montant_ht + tva
    
    return {
        'montant_ht': round(montant_ht, 2),
        'tva': round(tva, 2),
        'montant_ttc': round(montant_ttc, 2)
    }

def calculer_tva_deductible(montant_ttc, taux_tva=0.20):
    """
    Calculer la TVA déductible sur les achats
    
    Args:
        montant_ttc (float): Montant toutes taxes comprises
        taux_tva (float): Taux de TVA (20% par défaut)
    
    Returns:
        dict: Montant HT, TVA et TTC
    """
    montant_ht = montant_ttc / (1 + taux_tva)
    tva = montant_ttc - montant_ht
    
    return {
        'montant_ht': round(montant_ht, 2),
        'tva': round(tva, 2),
        'montant_ttc': round(montant_ttc, 2)
    }

def calculer_is_annuel(benefice_imposable, taux_is=0.31):
    """
    Calculer l'impôt sur les sociétés
    
    Args:
        benefice_imposable (float): Bénéfice imposable annuel
        taux_is (float): Taux IS (31% par défaut au Maroc)
    
    Returns:
        float: Montant de l'IS
    """
    if benefice_imposable <= 0:
        return 0.0
    
    is_amount = benefice_imposable * taux_is
    return round(is_amount, 2)
