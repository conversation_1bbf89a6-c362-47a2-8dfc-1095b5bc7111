#!/usr/bin/env python3
"""
Test des nouvelles fonctionnalités de rapports
"""

import sys
import os
import json

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rapport_stocks():
    """Tester le rapport des stocks"""
    print("🧪 Test du rapport des stocks...")
    
    try:
        from utils.rapports import generer_rapport_stocks
        
        rapport = generer_rapport_stocks()
        
        # Vérifier la structure du rapport
        assert 'nombre_produits' in rapport
        assert 'valeur_stock_total' in rapport
        assert 'stocks_faibles' in rapport
        assert 'detail_stocks' in rapport
        
        print(f"✅ Rapport généré avec {rapport['nombre_produits']} produits")
        print(f"   Valeur totale: {rapport['valeur_stock_total']:.2f} DH")
        print(f"   Stocks faibles: {rapport['stocks_faibles']['nombre']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_rapport_creances():
    """Tester le rapport des créances"""
    print("\n🧪 Test du rapport des créances...")
    
    try:
        from utils.rapports import generer_rapport_clients
        
        rapport = generer_rapport_clients()
        
        # Vérifier la structure du rapport
        assert 'nombre_clients_total' in rapport
        assert 'nombre_clients_creances' in rapport
        assert 'total_creances' in rapport
        assert 'clients_avec_creances' in rapport
        
        print(f"✅ Rapport généré avec {rapport['nombre_clients_total']} clients")
        print(f"   Clients avec créances: {rapport['nombre_clients_creances']}")
        print(f"   Total créances: {rapport['total_creances']:.2f} DH")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_rapport_dettes():
    """Tester le rapport des dettes"""
    print("\n🧪 Test du rapport des dettes...")
    
    try:
        from utils.rapports import generer_rapport_fournisseurs
        
        rapport = generer_rapport_fournisseurs()
        
        # Vérifier la structure du rapport
        assert 'nombre_fournisseurs_total' in rapport
        assert 'nombre_fournisseurs_dettes' in rapport
        assert 'total_dettes' in rapport
        assert 'fournisseurs_avec_dettes' in rapport
        
        print(f"✅ Rapport généré avec {rapport['nombre_fournisseurs_total']} fournisseurs")
        print(f"   Fournisseurs avec dettes: {rapport['nombre_fournisseurs_dettes']}")
        print(f"   Total dettes: {rapport['total_dettes']:.2f} DH")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_routes_flask():
    """Tester les routes Flask"""
    print("\n🧪 Test des routes Flask...")
    
    try:
        from main import app
        
        with app.test_client() as client:
            # Test route rapport stocks JSON
            response = client.get('/rapports/stocks')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert 'nombre_produits' in data
            print("✅ Route /rapports/stocks OK")
            
            # Test route rapport créances JSON
            response = client.get('/rapports/creances')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert 'nombre_clients_total' in data
            print("✅ Route /rapports/creances OK")
            
            # Test route rapport dettes JSON
            response = client.get('/rapports/dettes')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert 'nombre_fournisseurs_total' in data
            print("✅ Route /rapports/dettes OK")
            
            # Test route page détaillée stocks
            response = client.get('/rapports/stocks/detail')
            assert response.status_code == 200
            assert b'Rapport des Stocks' in response.data
            print("✅ Route /rapports/stocks/detail OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_donnees_exemple():
    """Vérifier que les données d'exemple sont présentes"""
    print("\n🧪 Vérification des données d'exemple...")
    
    try:
        from models.produit import Produit
        from models.client import Client
        from models.fournisseur import Fournisseur
        
        produits = Produit.get_all()
        clients = Client.get_all()
        fournisseurs = Fournisseur.get_all()
        
        print(f"✅ {len(produits)} produits trouvés")
        print(f"✅ {len(clients)} clients trouvés")
        print(f"✅ {len(fournisseurs)} fournisseurs trouvés")
        
        if len(produits) == 0:
            print("⚠️ Aucun produit trouvé. Exécutez 'python init_data.py' pour ajouter des données d'exemple.")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("="*60)
    print("🧪 TEST DES NOUVELLES FONCTIONNALITÉS DE RAPPORTS")
    print("="*60)
    
    tests = [
        ("Données d'exemple", test_donnees_exemple),
        ("Rapport des stocks", test_rapport_stocks),
        ("Rapport des créances", test_rapport_creances),
        ("Rapport des dettes", test_rapport_dettes),
        ("Routes Flask", test_routes_flask)
    ]
    
    resultats = []
    
    for nom_test, fonction_test in tests:
        try:
            resultat = fonction_test()
            resultats.append((nom_test, resultat))
        except Exception as e:
            print(f"❌ Erreur lors du test '{nom_test}': {e}")
            resultats.append((nom_test, False))
    
    # Résumé
    print("\n" + "="*60)
    print("📋 RÉSUMÉ DES TESTS")
    print("="*60)
    
    succes = 0
    for nom, resultat in resultats:
        statut = "✅ RÉUSSI" if resultat else "❌ ÉCHEC"
        print(f"{statut:12} {nom}")
        if resultat:
            succes += 1
    
    print(f"\n📊 Résultat: {succes}/{len(resultats)} tests réussis")
    
    if succes == len(resultats):
        print("\n🎉 TOUTES LES NOUVELLES FONCTIONNALITÉS FONCTIONNENT!")
        print("🌐 Testez dans le navigateur:")
        print("   • http://localhost:5000/rapports")
        print("   • Cliquez sur 'Rapport Rapide' ou 'Vue Détaillée' pour les stocks")
        print("   • Testez les rapports de créances et dettes")
    else:
        print(f"\n⚠️ {len(resultats) - succes} problème(s) détecté(s)")
    
    return succes == len(resultats)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
