"""
Configuration et initialisation de la base de données SQLite
pour le système de comptabilité de l'entreprise de carburants
"""

import sqlite3
import os
from datetime import datetime

class Database:
    def __init__(self, db_path="data/fuel_company.db"):
        self.db_path = db_path
        self.ensure_data_directory()
        self.init_database()
    
    def ensure_data_directory(self):
        """Créer le dossier data s'il n'existe pas"""
        data_dir = os.path.dirname(self.db_path)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def get_connection(self):
        """Obtenir une connexion à la base de données"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """Initialiser toutes les tables de la base de données"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Table clients
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                telephone TEXT,
                adresse TEXT,
                solde REAL DEFAULT 0.0,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table fournisseurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                telephone TEXT,
                adresse TEXT,
                solde REAL DEFAULT 0.0,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table produits (carburants)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS produits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prix_achat REAL NOT NULL,
                prix_vente REAL NOT NULL,
                quantite REAL DEFAULT 0.0,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table transactions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type TEXT NOT NULL CHECK (type IN ('achat', 'vente')),
                type_entite TEXT NOT NULL CHECK (type_entite IN ('client', 'fournisseur')),
                entite_id INTEGER NOT NULL,
                produit_id INTEGER NOT NULL,
                quantite REAL NOT NULL,
                prix_unitaire REAL NOT NULL,
                total REAL NOT NULL,
                date_transaction TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (produit_id) REFERENCES produits (id)
            )
        ''')
        
        # Table employés
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                cin TEXT UNIQUE NOT NULL,
                numero_cnss TEXT,
                salaire_brut REAL NOT NULL,
                salaire_net REAL,
                cnss REAL,
                amo REAL,
                ir REAL,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table déclarations fiscales
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS declarations_fiscales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type_impot TEXT NOT NULL CHECK (type_impot IN ('TVA', 'IS', 'IR')),
                periode TEXT NOT NULL,
                montant REAL NOT NULL,
                date_declaration TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table déclarations sociales
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS declarations_sociales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employe_id INTEGER NOT NULL,
                mois TEXT NOT NULL,
                cnss REAL NOT NULL,
                amo REAL NOT NULL,
                ir REAL NOT NULL,
                total REAL NOT NULL,
                date_declaration TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employe_id) REFERENCES employes (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("Base de données initialisée avec succès!")

# Instance globale de la base de données
db = Database()
