#!/usr/bin/env python3
"""
Script de démarrage pour le système de comptabilité
"""

import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("=== SYSTÈME DE COMPTABILITÉ POUR ENTREPRISE DE CARBURANTS ===")
    print("Initialisation en cours...")
    
    # Importer l'application
    from main import app
    
    print("✓ Application Flask initialisée")
    print("✓ Base de données configurée")
    print("✓ Modèles chargés")
    
    print("\n" + "="*60)
    print("🚀 DÉMARRAGE DU SERVEUR")
    print("="*60)
    print("📍 URL: http://localhost:5000")
    print("🔧 Mode: Développement")
    print("⚡ Hot reload: Activé")
    print("="*60)
    print("\n💡 Appuyez sur Ctrl+C pour arrêter le serveur")
    print("\n🌐 Ouvrez votre navigateur et accédez à: http://localhost:5000")
    print("\n")
    
    # Démarrer l'application
    app.run(
        debug=True,
        host='127.0.0.1',
        port=5000,
        use_reloader=False  # Désactiver le reloader pour éviter les problèmes
    )
    
except ImportError as e:
    print(f"❌ Erreur d'importation: {e}")
    print("\n🔧 Solutions possibles:")
    print("1. Vérifiez que tous les fichiers sont présents")
    print("2. Installez Flask: pip install flask")
    print("3. Vérifiez la structure des dossiers")
    
except Exception as e:
    print(f"❌ Erreur lors du démarrage: {e}")
    import traceback
    traceback.print_exc()
    
finally:
    print("\n👋 Arrêt du système de comptabilité")
