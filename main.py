"""
Application Flask principale pour le système de comptabilité
de l'entreprise de carburants
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from datetime import datetime
import os

# Import des modèles
from models.database import db
from models.client import Client
from models.fournisseur import Fournisseur
from models.produit import Produit
from models.transaction import Transaction
from models.employe import Employe
from models.declaration_fiscale import DeclarationFiscale
from models.declaration_sociale import DeclarationSociale

# Import des utilitaires
from utils.calculs import calculer_salaire, calculer_tva_collectee, calculer_is_annuel
from utils.rapports import generer_rapport_mensuel, generer_bilan_annuel, generer_rapport_stocks

app = Flask(__name__)
app.secret_key = 'votre_cle_secrete_ici'  # Changez cette clé en production

@app.route('/')
def index():
    """Page d'accueil avec tableau de bord"""
    # Statistiques rapides
    clients = Client.get_all()
    fournisseurs = Fournisseur.get_all()
    produits = Produit.get_all()
    employes = Employe.get_all()

    # Transactions récentes
    transactions_recentes = Transaction.get_all()[:10]  # 10 dernières transactions

    # CA du mois en cours
    maintenant = datetime.now()
    ca_mois_courant = Transaction.calculer_ca_mensuel(maintenant.month, maintenant.year)

    stats = {
        'nb_clients': len(clients),
        'nb_fournisseurs': len(fournisseurs),
        'nb_produits': len(produits),
        'nb_employes': len(employes),
        'ca_mois_courant': ca_mois_courant,
        'transactions_recentes': transactions_recentes
    }

    return render_template('index.html', stats=stats)

# Routes pour les clients
@app.route('/clients')
def clients():
    """Page de gestion des clients"""
    clients_list = Client.get_all()
    return render_template('clients.html', clients=clients_list)

@app.route('/clients/ajouter', methods=['POST'])
def ajouter_client():
    """Ajouter un nouveau client"""
    nom = request.form['nom']
    telephone = request.form.get('telephone', '')
    adresse = request.form.get('adresse', '')

    client = Client(nom, telephone, adresse)
    client.save()

    flash('Client ajouté avec succès!', 'success')
    return redirect(url_for('clients'))

@app.route('/clients/modifier/<int:client_id>', methods=['POST'])
def modifier_client(client_id):
    """Modifier un client existant"""
    nom = request.form['nom']
    telephone = request.form.get('telephone', '')
    adresse = request.form.get('adresse', '')
    solde = float(request.form.get('solde', 0))

    Client.update(client_id, nom, telephone, adresse, solde)

    flash('Client modifié avec succès!', 'success')
    return redirect(url_for('clients'))

@app.route('/clients/supprimer/<int:client_id>')
def supprimer_client(client_id):
    """Supprimer un client"""
    Client.delete(client_id)
    flash('Client supprimé avec succès!', 'success')
    return redirect(url_for('clients'))

# Routes pour les fournisseurs
@app.route('/fournisseurs')
def fournisseurs():
    """Page de gestion des fournisseurs"""
    fournisseurs_list = Fournisseur.get_all()
    return render_template('fournisseurs.html', fournisseurs=fournisseurs_list)

@app.route('/fournisseurs/ajouter', methods=['POST'])
def ajouter_fournisseur():
    """Ajouter un nouveau fournisseur"""
    nom = request.form['nom']
    telephone = request.form.get('telephone', '')
    adresse = request.form.get('adresse', '')

    fournisseur = Fournisseur(nom, telephone, adresse)
    fournisseur.save()

    flash('Fournisseur ajouté avec succès!', 'success')
    return redirect(url_for('fournisseurs'))

@app.route('/fournisseurs/modifier/<int:fournisseur_id>', methods=['POST'])
def modifier_fournisseur(fournisseur_id):
    """Modifier un fournisseur existant"""
    nom = request.form['nom']
    telephone = request.form.get('telephone', '')
    adresse = request.form.get('adresse', '')
    solde = float(request.form.get('solde', 0))

    Fournisseur.update(fournisseur_id, nom, telephone, adresse, solde)

    flash('Fournisseur modifié avec succès!', 'success')
    return redirect(url_for('fournisseurs'))

@app.route('/fournisseurs/supprimer/<int:fournisseur_id>')
def supprimer_fournisseur(fournisseur_id):
    """Supprimer un fournisseur"""
    Fournisseur.delete(fournisseur_id)
    flash('Fournisseur supprimé avec succès!', 'success')
    return redirect(url_for('fournisseurs'))

# Routes pour les produits
@app.route('/produits')
def produits():
    """Page de gestion des produits"""
    produits_list = Produit.get_all()
    return render_template('produits.html', produits=produits_list)

@app.route('/produits/ajouter', methods=['POST'])
def ajouter_produit():
    """Ajouter un nouveau produit"""
    nom = request.form['nom']
    prix_achat = float(request.form['prix_achat'])
    prix_vente = float(request.form['prix_vente'])
    quantite = float(request.form.get('quantite', 0))

    produit = Produit(nom, prix_achat, prix_vente, quantite)
    produit.save()

    flash('Produit ajouté avec succès!', 'success')
    return redirect(url_for('produits'))

@app.route('/produits/modifier/<int:produit_id>', methods=['POST'])
def modifier_produit(produit_id):
    """Modifier un produit existant"""
    nom = request.form['nom']
    prix_achat = float(request.form['prix_achat'])
    prix_vente = float(request.form['prix_vente'])
    quantite = float(request.form['quantite'])

    Produit.update(produit_id, nom, prix_achat, prix_vente, quantite)

    flash('Produit modifié avec succès!', 'success')
    return redirect(url_for('produits'))

# Routes pour les transactions
@app.route('/transactions')
def transactions():
    """Page de gestion des transactions"""
    transactions_list = Transaction.get_all()
    clients_list = Client.get_all()
    fournisseurs_list = Fournisseur.get_all()
    produits_list = Produit.get_all()

    return render_template('transactions.html',
                         transactions=transactions_list,
                         clients=clients_list,
                         fournisseurs=fournisseurs_list,
                         produits=produits_list)

@app.route('/transactions/ajouter', methods=['POST'])
def ajouter_transaction():
    """Ajouter une nouvelle transaction"""
    type_transaction = request.form['type']
    type_entite = request.form['type_entite']
    entite_id = int(request.form['entite_id'])
    produit_id = int(request.form['produit_id'])
    quantite = float(request.form['quantite'])
    prix_unitaire = float(request.form['prix_unitaire'])

    transaction = Transaction(type_transaction, type_entite, entite_id,
                            produit_id, quantite, prix_unitaire)
    transaction.save()

    flash('Transaction enregistrée avec succès!', 'success')
    return redirect(url_for('transactions'))

# Routes pour les employés
@app.route('/employes')
def employes():
    """Page de gestion des employés"""
    employes_list = Employe.get_all()
    return render_template('employes.html', employes=employes_list)

@app.route('/employes/ajouter', methods=['POST'])
def ajouter_employe():
    """Ajouter un nouvel employé"""
    nom = request.form['nom']
    cin = request.form['cin']
    numero_cnss = request.form.get('numero_cnss', '')
    salaire_brut = float(request.form['salaire_brut'])

    employe = Employe(nom, cin, numero_cnss, salaire_brut)
    employe.save()

    flash('Employé ajouté avec succès!', 'success')
    return redirect(url_for('employes'))

@app.route('/employes/modifier/<int:employe_id>', methods=['POST'])
def modifier_employe(employe_id):
    """Modifier un employé existant"""
    nom = request.form['nom']
    cin = request.form['cin']
    numero_cnss = request.form.get('numero_cnss', '')
    salaire_brut = float(request.form['salaire_brut'])

    Employe.update(employe_id, nom, cin, numero_cnss, salaire_brut)

    flash('Employé modifié avec succès!', 'success')
    return redirect(url_for('employes'))

# Routes pour les rapports
@app.route('/rapports')
def rapports():
    """Page des rapports"""
    return render_template('rapports.html')

@app.route('/rapports/mensuel')
def rapport_mensuel():
    """Générer un rapport mensuel"""
    mois = int(request.args.get('mois', datetime.now().month))
    annee = int(request.args.get('annee', datetime.now().year))

    rapport = generer_rapport_mensuel(mois, annee)
    return jsonify(rapport)

@app.route('/rapports/annuel')
def rapport_annuel():
    """Générer un rapport annuel"""
    annee = int(request.args.get('annee', datetime.now().year))

    bilan = generer_bilan_annuel(annee)
    return jsonify(bilan)

if __name__ == '__main__':
    try:
        # Initialiser la base de données au démarrage
        print("Initialisation de la base de données...")

        # Lancer l'application
        print("Démarrage de l'application...")
        print("Accédez à l'application sur: http://localhost:5000")
        app.run(debug=True, host='127.0.0.1', port=5000)
    except Exception as e:
        print(f"Erreur lors du démarrage: {e}")
        import traceback
        traceback.print_exc()
