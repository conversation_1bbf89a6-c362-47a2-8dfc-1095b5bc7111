{% extends "base.html" %}

{% block title %}Gestion des Fournisseurs - Système de Comptabilité{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-truck"></i> Gestion des Fournisseurs</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterFournisseurModal">
            <i class="fas fa-plus"></i> Ajouter Fournisseur
        </button>
    </div>
</div>

<!-- Liste des fournisseurs -->
<div class="card shadow">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold"><i class="fas fa-list"></i> Liste des Fournisseurs</h6>
    </div>
    <div class="card-body">
        {% if fournisseurs %}
        <div class="table-responsive">
            <table class="table table-bordered" id="fournisseursTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Téléphone</th>
                        <th>Adresse</th>
                        <th>Solde (DH)</th>
                        <th>Date Création</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for fournisseur in fournisseurs %}
                    <tr>
                        <td>{{ fournisseur[0] }}</td>
                        <td>{{ fournisseur[1] }}</td>
                        <td>{{ fournisseur[2] or 'N/A' }}</td>
                        <td>{{ fournisseur[3] or 'N/A' }}</td>
                        <td>
                            <span class="badge bg-{{ 'danger' if fournisseur[4] < 0 else 'success' }}">
                                {{ "{:,.2f}".format(fournisseur[4]) }}
                            </span>
                        </td>
                        <td>{{ fournisseur[5][:10] if fournisseur[5] else 'N/A' }}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="modifierFournisseur({{ fournisseur[0] }}, '{{ fournisseur[1] }}', '{{ fournisseur[2] or '' }}', '{{ fournisseur[3] or '' }}', {{ fournisseur[4] }})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <a href="{{ url_for('supprimer_fournisseur', fournisseur_id=fournisseur[0]) }}" 
                               class="btn btn-sm btn-danger" 
                               onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce fournisseur ?')">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-muted">Aucun fournisseur enregistré.</p>
        {% endif %}
    </div>
</div>

<!-- Modal Ajouter Fournisseur -->
<div class="modal fade" id="ajouterFournisseurModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-truck"></i> Ajouter un Fournisseur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('ajouter_fournisseur') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nom" class="form-label">Nom *</label>
                        <input type="text" class="form-control" id="nom" name="nom" required>
                    </div>
                    <div class="mb-3">
                        <label for="telephone" class="form-label">Téléphone</label>
                        <input type="text" class="form-control" id="telephone" name="telephone">
                    </div>
                    <div class="mb-3">
                        <label for="adresse" class="form-label">Adresse</label>
                        <textarea class="form-control" id="adresse" name="adresse" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Modifier Fournisseur -->
<div class="modal fade" id="modifierFournisseurModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Modifier le Fournisseur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="modifierFournisseurForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="modif_nom" class="form-label">Nom *</label>
                        <input type="text" class="form-control" id="modif_nom" name="nom" required>
                    </div>
                    <div class="mb-3">
                        <label for="modif_telephone" class="form-label">Téléphone</label>
                        <input type="text" class="form-control" id="modif_telephone" name="telephone">
                    </div>
                    <div class="mb-3">
                        <label for="modif_adresse" class="form-label">Adresse</label>
                        <textarea class="form-control" id="modif_adresse" name="adresse" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="modif_solde" class="form-label">Solde (DH)</label>
                        <input type="number" step="0.01" class="form-control" id="modif_solde" name="solde">
                        <div class="form-text">Solde négatif = dette envers le fournisseur</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-warning">Modifier</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<script>
function modifierFournisseur(id, nom, telephone, adresse, solde) {
    document.getElementById('modif_nom').value = nom;
    document.getElementById('modif_telephone').value = telephone;
    document.getElementById('modif_adresse').value = adresse;
    document.getElementById('modif_solde').value = solde;
    
    document.getElementById('modifierFournisseurForm').action = '/fournisseurs/modifier/' + id;
    
    var modal = new bootstrap.Modal(document.getElementById('modifierFournisseurModal'));
    modal.show();
}

// DataTable
$(document).ready(function() {
    $('#fournisseursTable').DataTable({
        "language": {
            "search": "Rechercher:",
            "lengthMenu": "Afficher _MENU_ entrées",
            "info": "Affichage de _START_ à _END_ sur _TOTAL_ entrées",
            "paginate": {
                "first": "Premier",
                "last": "Dernier",
                "next": "Suivant",
                "previous": "Précédent"
            }
        },
        "pageLength": 25,
        "order": [[ 1, "asc" ]]
    });
});
</script>
{% endblock %}
