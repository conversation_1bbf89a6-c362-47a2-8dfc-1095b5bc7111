{% extends "base.html" %}

{% block title %}Gestion des Employés - Système de Comptabilité{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-user-tie"></i> Gestion des Employés</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterEmployeModal">
            <i class="fas fa-plus"></i> Ajouter Employé
        </button>
    </div>
</div>

<!-- Résumé masse salariale -->
{% if employes %}
{% set total_brut = employes|sum(attribute=4) %}
{% set total_net = employes|sum(attribute=5) %}
{% set total_cnss = employes|sum(attribute=6) %}
{% set total_amo = employes|sum(attribute=7) %}
{% set total_ir = employes|sum(attribute=8) %}

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5>Salaire Brut Total</h5>
                <h3>{{ "{:,.2f}".format(total_brut) }} DH</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5>Salaire Net Total</h5>
                <h3>{{ "{:,.2f}".format(total_net) }} DH</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <h5>Total CNSS</h5>
                <h3>{{ "{:,.2f}".format(total_cnss) }} DH</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h5>Total IR</h5>
                <h3>{{ "{:,.2f}".format(total_ir) }} DH</h3>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Liste des employés -->
<div class="card shadow">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold"><i class="fas fa-list"></i> Liste des Employés</h6>
    </div>
    <div class="card-body">
        {% if employes %}
        <div class="table-responsive">
            <table class="table table-bordered" id="employesTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>CIN</th>
                        <th>N° CNSS</th>
                        <th>Salaire Brut</th>
                        <th>CNSS</th>
                        <th>AMO</th>
                        <th>IR</th>
                        <th>Salaire Net</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employe in employes %}
                    <tr>
                        <td>{{ employe[0] }}</td>
                        <td>{{ employe[1] }}</td>
                        <td>{{ employe[2] }}</td>
                        <td>{{ employe[3] or 'N/A' }}</td>
                        <td>{{ "{:,.2f}".format(employe[4]) }} DH</td>
                        <td>{{ "{:,.2f}".format(employe[6]) }} DH</td>
                        <td>{{ "{:,.2f}".format(employe[7]) }} DH</td>
                        <td>{{ "{:,.2f}".format(employe[8]) }} DH</td>
                        <td><strong>{{ "{:,.2f}".format(employe[5]) }} DH</strong></td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="modifierEmploye({{ employe[0] }}, '{{ employe[1] }}', '{{ employe[2] }}', '{{ employe[3] or '' }}', {{ employe[4] }})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info" onclick="voirFichePaie({{ employe[0] }}, '{{ employe[1] }}')">
                                <i class="fas fa-file-alt"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-muted">Aucun employé enregistré.</p>
        {% endif %}
    </div>
</div>

<!-- Modal Ajouter Employé -->
<div class="modal fade" id="ajouterEmployeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user-plus"></i> Ajouter un Employé</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('ajouter_employe') }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nom" class="form-label">Nom Complet *</label>
                                <input type="text" class="form-control" id="nom" name="nom" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cin" class="form-label">CIN *</label>
                                <input type="text" class="form-control" id="cin" name="cin" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="numero_cnss" class="form-label">Numéro CNSS</label>
                                <input type="text" class="form-control" id="numero_cnss" name="numero_cnss">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="salaire_brut" class="form-label">Salaire Brut (DH) *</label>
                                <input type="number" step="0.01" class="form-control" id="salaire_brut" name="salaire_brut" required onchange="calculerSalaire()">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Aperçu des calculs -->
                    <div class="card bg-light">
                        <div class="card-header">
                            <h6 class="mb-0">Aperçu des Calculs</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">CNSS (4.48%)</label>
                                    <input type="text" class="form-control" id="apercu_cnss" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">AMO (2.26%)</label>
                                    <input type="text" class="form-control" id="apercu_amo" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">IR</label>
                                    <input type="text" class="form-control" id="apercu_ir" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label"><strong>Salaire Net</strong></label>
                                    <input type="text" class="form-control fw-bold" id="apercu_net" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Modifier Employé -->
<div class="modal fade" id="modifierEmployeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Modifier l'Employé</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="modifierEmployeForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modif_nom" class="form-label">Nom Complet *</label>
                                <input type="text" class="form-control" id="modif_nom" name="nom" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modif_cin" class="form-label">CIN *</label>
                                <input type="text" class="form-control" id="modif_cin" name="cin" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modif_numero_cnss" class="form-label">Numéro CNSS</label>
                                <input type="text" class="form-control" id="modif_numero_cnss" name="numero_cnss">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modif_salaire_brut" class="form-label">Salaire Brut (DH) *</label>
                                <input type="number" step="0.01" class="form-control" id="modif_salaire_brut" name="salaire_brut" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-warning">Modifier</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Fiche de Paie -->
<div class="modal fade" id="fichePaieModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-file-alt"></i> Fiche de Paie</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <h4>FICHE DE PAIE</h4>
                    <p class="text-muted">Entreprise de Carburants</p>
                </div>
                
                <div id="fichePaieContent">
                    <!-- Le contenu sera rempli par JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" onclick="imprimerFiche()">
                    <i class="fas fa-print"></i> Imprimer
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<script>
// Fonction pour calculer le salaire en temps réel
function calculerSalaire() {
    const salaireBrut = parseFloat(document.getElementById('salaire_brut').value) || 0;
    
    if (salaireBrut > 0) {
        // Calculs selon la réglementation marocaine
        const plafond = 6000;
        const baseCnssAmo = Math.min(salaireBrut, plafond);
        
        const cnss = baseCnssAmo * 0.0448;
        const amo = baseCnssAmo * 0.0226;
        const salaireImposable = salaireBrut - cnss - amo;
        
        // Calcul IR simplifié
        let ir = 0;
        if (salaireImposable > 2500) {
            if (salaireImposable <= 4166.67) {
                ir = (salaireImposable - 2500) * 0.10;
            } else if (salaireImposable <= 5000) {
                ir = 166.67 + (salaireImposable - 4166.67) * 0.20;
            } else if (salaireImposable <= 6666.67) {
                ir = 333.33 + (salaireImposable - 5000) * 0.30;
            } else if (salaireImposable <= 15000) {
                ir = 833.33 + (salaireImposable - 6666.67) * 0.34;
            } else {
                ir = 3666.67 + (salaireImposable - 15000) * 0.38;
            }
        }
        
        const salaireNet = salaireBrut - cnss - amo - ir;
        
        // Afficher les résultats
        document.getElementById('apercu_cnss').value = cnss.toFixed(2) + ' DH';
        document.getElementById('apercu_amo').value = amo.toFixed(2) + ' DH';
        document.getElementById('apercu_ir').value = ir.toFixed(2) + ' DH';
        document.getElementById('apercu_net').value = salaireNet.toFixed(2) + ' DH';
    } else {
        document.getElementById('apercu_cnss').value = '';
        document.getElementById('apercu_amo').value = '';
        document.getElementById('apercu_ir').value = '';
        document.getElementById('apercu_net').value = '';
    }
}

function modifierEmploye(id, nom, cin, numeroCnss, salaireBrut) {
    document.getElementById('modif_nom').value = nom;
    document.getElementById('modif_cin').value = cin;
    document.getElementById('modif_numero_cnss').value = numeroCnss;
    document.getElementById('modif_salaire_brut').value = salaireBrut;
    
    document.getElementById('modifierEmployeForm').action = '/employes/modifier/' + id;
    
    var modal = new bootstrap.Modal(document.getElementById('modifierEmployeModal'));
    modal.show();
}

function voirFichePaie(id, nom) {
    // Récupérer les données de l'employé depuis le tableau
    const row = document.querySelector(`tr td:first-child:contains('${id}')`);
    // Pour simplifier, on va créer une fiche de paie basique
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <strong>Employé:</strong> ${nom}<br>
                <strong>Période:</strong> ${new Date().toLocaleDateString('fr-FR', {month: 'long', year: 'numeric'})}
            </div>
            <div class="col-md-6 text-end">
                <strong>Date d'édition:</strong> ${new Date().toLocaleDateString('fr-FR')}
            </div>
        </div>
        <hr>
        <p class="text-center"><em>Fiche de paie détaillée à implémenter</em></p>
    `;
    
    document.getElementById('fichePaieContent').innerHTML = content;
    
    var modal = new bootstrap.Modal(document.getElementById('fichePaieModal'));
    modal.show();
}

function imprimerFiche() {
    window.print();
}

// DataTable
$(document).ready(function() {
    $('#employesTable').DataTable({
        "language": {
            "search": "Rechercher:",
            "lengthMenu": "Afficher _MENU_ entrées",
            "info": "Affichage de _START_ à _END_ sur _TOTAL_ entrées",
            "paginate": {
                "first": "Premier",
                "last": "Dernier",
                "next": "Suivant",
                "previous": "Précédent"
            }
        },
        "pageLength": 25,
        "order": [[ 1, "asc" ]]
    });
});
</script>
{% endblock %}
