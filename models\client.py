"""
Modèle Client pour la gestion des clients de l'entreprise de carburants
"""

from models.database import db

class Client:
    def __init__(self, nom, telephone=None, adresse=None, solde=0.0):
        self.nom = nom
        self.telephone = telephone
        self.adresse = adresse
        self.solde = solde
    
    def save(self):
        """Sauvegarder le client en base de données"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO clients (nom, telephone, adresse, solde)
            VALUES (?, ?, ?, ?)
        ''', (self.nom, self.telephone, self.adresse, self.solde))
        
        client_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return client_id
    
    @staticmethod
    def get_all():
        """Récupérer tous les clients"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM clients ORDER BY nom')
        clients = cursor.fetchall()
        conn.close()
        
        return clients
    
    @staticmethod
    def get_by_id(client_id):
        """Récupérer un client par son ID"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM clients WHERE id = ?', (client_id,))
        client = cursor.fetchone()
        conn.close()
        
        return client
    
    @staticmethod
    def update(client_id, nom, telephone=None, adresse=None, solde=None):
        """Mettre à jour un client"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE clients 
            SET nom = ?, telephone = ?, adresse = ?, solde = ?
            WHERE id = ?
        ''', (nom, telephone, adresse, solde, client_id))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def update_solde(client_id, nouveau_solde):
        """Mettre à jour le solde d'un client"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('UPDATE clients SET solde = ? WHERE id = ?', (nouveau_solde, client_id))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def delete(client_id):
        """Supprimer un client"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM clients WHERE id = ?', (client_id,))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def get_soldes_positifs():
        """Récupérer les clients avec un solde positif (créances)"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM clients WHERE solde > 0 ORDER BY solde DESC')
        clients = cursor.fetchall()
        conn.close()
        
        return clients
