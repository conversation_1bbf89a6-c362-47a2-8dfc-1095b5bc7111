#!/usr/bin/env python3
"""
Script d'initialisation avec des données d'exemple
pour le système de comptabilité
"""

import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.client import Client
from models.fournisseur import Fournisseur
from models.produit import Produit
from models.employe import Employe
from models.transaction import Transaction

def init_sample_data():
    """Initialiser le système avec des données d'exemple"""
    
    print("🔄 Initialisation des données d'exemple...")
    
    try:
        # 1. Ajouter des produits (carburants)
        print("📦 Ajout des produits...")
        
        essence = Produit("Essence Super", 12.50, 15.00, 5000)
        essence.save()
        
        gasoil = Produit("Gasoil", 11.80, 14.20, 8000)
        gasoil.save()
        
        gaz = Produit("Gaz Butane", 45.00, 55.00, 200)
        gaz.save()
        
        print("✓ 3 produits ajoutés")
        
        # 2. Ajouter des clients
        print("👥 Ajout des clients...")
        
        client1 = Client("Transport Alami", "0661234567", "Casablanca", 0)
        client1.save()
        
        client2 = Client("Société Logistique Maroc", "0662345678", "Rabat", 0)
        client2.save()
        
        client3 = Client("Taxi Hassan", "0663456789", "Fès", 0)
        client3.save()
        
        print("✓ 3 clients ajoutés")
        
        # 3. Ajouter des fournisseurs
        print("🚛 Ajout des fournisseurs...")
        
        fournisseur1 = Fournisseur("Raffinerie Samir", "0524123456", "Mohammedia", 0)
        fournisseur1.save()
        
        fournisseur2 = Fournisseur("Afriquia SMDC", "0524234567", "Casablanca", 0)
        fournisseur2.save()
        
        print("✓ 2 fournisseurs ajoutés")
        
        # 4. Ajouter des employés
        print("👨‍💼 Ajout des employés...")
        
        employe1 = Employe("Ahmed Benali", "AB123456", "1234567890", 8000)
        employe1.save()
        
        employe2 = Employe("Fatima Zahra", "FZ789012", "0987654321", 6500)
        employe2.save()
        
        employe3 = Employe("Mohamed Alaoui", "MA345678", "1122334455", 5500)
        employe3.save()
        
        print("✓ 3 employés ajoutés")
        
        # 5. Ajouter quelques transactions d'exemple
        print("💰 Ajout de transactions d'exemple...")
        
        # Achat d'essence
        transaction1 = Transaction("achat", "fournisseur", 1, 1, 1000, 12.50)
        transaction1.save()
        
        # Vente d'essence
        transaction2 = Transaction("vente", "client", 1, 1, 500, 15.00)
        transaction2.save()
        
        # Achat de gasoil
        transaction3 = Transaction("achat", "fournisseur", 2, 2, 2000, 11.80)
        transaction3.save()
        
        # Vente de gasoil
        transaction4 = Transaction("vente", "client", 2, 2, 800, 14.20)
        transaction4.save()
        
        print("✓ 4 transactions ajoutées")
        
        print("\n" + "="*50)
        print("🎉 DONNÉES D'EXEMPLE INITIALISÉES AVEC SUCCÈS!")
        print("="*50)
        print("\n📊 Résumé des données ajoutées:")
        print(f"   • 3 produits (carburants)")
        print(f"   • 3 clients")
        print(f"   • 2 fournisseurs") 
        print(f"   • 3 employés")
        print(f"   • 4 transactions")
        print("\n🌐 Vous pouvez maintenant tester l'application:")
        print("   python run.py")
        print("   http://localhost:5000")
        
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        import traceback
        traceback.print_exc()

def reset_database():
    """Réinitialiser la base de données"""
    import os
    db_path = "data/fuel_company.db"
    
    if os.path.exists(db_path):
        os.remove(db_path)
        print("🗑️ Base de données supprimée")
    
    # Réimporter pour recréer la base
    from models.database import db
    print("🔄 Base de données recréée")

if __name__ == "__main__":
    print("=== INITIALISATION DU SYSTÈME DE COMPTABILITÉ ===\n")
    
    if len(sys.argv) > 1 and sys.argv[1] == "--reset":
        print("⚠️ ATTENTION: Ceci va supprimer toutes les données existantes!")
        confirm = input("Tapez 'OUI' pour confirmer: ")
        if confirm == "OUI":
            reset_database()
            init_sample_data()
        else:
            print("❌ Opération annulée")
    else:
        init_sample_data()
    
    print("\n👋 Initialisation terminée!")
