{% extends "base.html" %}

{% block title %}Rapport des Stocks - Système de Comptabilité{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-boxes"></i> Rapport des Stocks</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-secondary me-2" onclick="window.print()">
            <i class="fas fa-print"></i> Imprimer
        </button>
        <button type="button" class="btn btn-primary" onclick="exporterCSV('stocksTable', 'rapport_stocks.csv')">
            <i class="fas fa-download"></i> Exporter CSV
        </button>
    </div>
</div>

<!-- Alertes stock faible -->
{% set stocks_faibles = [] %}
{% for produit in produits %}
    {% if produit[4] < 100 %}
        {% set _ = stocks_faibles.append(produit) %}
    {% endif %}
{% endfor %}

{% if stocks_faibles %}
<div class="alert alert-warning" role="alert">
    <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Alertes Stock Faible</h4>
    <p>Les produits suivants ont un stock inférieur à 100 unités :</p>
    <div class="table-responsive">
        <table class="table table-sm mb-0">
            <thead>
                <tr>
                    <th>Produit</th>
                    <th>Stock Actuel</th>
                    <th>Prix d'Achat</th>
                    <th>Valeur Stock</th>
                    <th>Action Recommandée</th>
                </tr>
            </thead>
            <tbody>
                {% for produit in stocks_faibles %}
                <tr>
                    <td><strong>{{ produit[1] }}</strong></td>
                    <td><span class="badge bg-danger">{{ "{:,.0f}".format(produit[4]) }} unités</span></td>
                    <td>{{ "{:,.2f}".format(produit[2]) }} DH</td>
                    <td>{{ "{:,.2f}".format(produit[4] * produit[2]) }} DH</td>
                    <td><span class="badge bg-warning">Réapprovisionner</span></td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Résumé des stocks -->
<div class="row mb-4">
    {% set nb_stocks_faibles = stocks_faibles|length %}
    {% set valeur_totale = namespace(value=0) %}
    {% set stock_total = namespace(value=0) %}
    {% for produit in produits %}
        {% set valeur_totale.value = valeur_totale.value + (produit[4] * produit[2]) %}
        {% set stock_total.value = stock_total.value + produit[4] %}
    {% endfor %}

    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5>Nombre de Produits</h5>
                <h3>{{ produits|length }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5>Valeur Totale Stock</h5>
                <h3>{{ "{:,.0f}".format(valeur_totale.value) }} DH</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-{{ 'danger' if nb_stocks_faibles > 0 else 'success' }} text-white">
            <div class="card-body">
                <h5>Stocks Faibles</h5>
                <h3>{{ nb_stocks_faibles }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h5>Stock Moyen</h5>
                <h3>{{ "{:,.0f}".format(stock_total.value / produits|length if produits else 0) }}</h3>
            </div>
        </div>
    </div>
</div>

<!-- Détail des stocks -->
<div class="card shadow">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold"><i class="fas fa-list"></i> Détail des Stocks par Produit</h6>
    </div>
    <div class="card-body">
        {% if produits %}
        <div class="table-responsive">
            <table class="table table-bordered" id="stocksTable">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th>Stock Actuel</th>
                        <th>Prix d'Achat (DH)</th>
                        <th>Prix de Vente (DH)</th>
                        <th>Marge (%)</th>
                        <th>Valeur Stock (DH)</th>
                        <th>Statut</th>
                        <th>Rotation</th>
                    </tr>
                </thead>
                <tbody>
                    {% for produit in produits %}
                    {% set marge = ((produit[3] - produit[2]) / produit[2] * 100) if produit[2] > 0 else 0 %}
                    {% set valeur_stock = produit[4] * produit[2] %}
                    {% set statut_stock = 'Critique' if produit[4] < 50 else 'Faible' if produit[4] < 100 else 'Moyen' if produit[4] < 500 else 'Bon' %}
                    {% set classe_statut = 'danger' if produit[4] < 50 else 'warning' if produit[4] < 100 else 'info' if produit[4] < 500 else 'success' %}
                    <tr>
                        <td><strong>{{ produit[1] }}</strong></td>
                        <td>
                            <span class="badge bg-{{ 'danger' if produit[4] < 100 else 'warning' if produit[4] < 500 else 'success' }}">
                                {{ "{:,.0f}".format(produit[4]) }} unités
                            </span>
                        </td>
                        <td>{{ "{:,.2f}".format(produit[2]) }}</td>
                        <td>{{ "{:,.2f}".format(produit[3]) }}</td>
                        <td>
                            <span class="badge bg-{{ 'success' if marge > 20 else 'warning' if marge > 10 else 'danger' }}">
                                {{ "{:,.1f}".format(marge) }}%
                            </span>
                        </td>
                        <td><strong>{{ "{:,.2f}".format(valeur_stock) }}</strong></td>
                        <td>
                            <span class="badge bg-{{ classe_statut }}">{{ statut_stock }}</span>
                        </td>
                        <td>
                            {% if produit[4] > 0 %}
                                <span class="badge bg-secondary">Normal</span>
                            {% else %}
                                <span class="badge bg-danger">Rupture</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th>TOTAL</th>
                        <th>{{ "{:,.0f}".format(produits|sum(attribute=4)) }} unités</th>
                        <th colspan="3">Valeur moyenne</th>
                        <th><strong>
                            {% set valeur_totale = namespace(value=0) %}
                            {% for produit in produits %}
                                {% set valeur_totale.value = valeur_totale.value + (produit[4] * produit[2]) %}
                            {% endfor %}
                            {{ "{:,.2f}".format(valeur_totale.value) }} DH
                        </strong></th>
                        <th colspan="2"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <p class="text-muted">Aucun produit enregistré.</p>
        {% endif %}
    </div>
</div>

<!-- Recommandations -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Recommandations</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-danger">Actions Urgentes</h6>
                        <ul class="list-unstyled">
                            {% for produit in stocks_faibles %}
                            <li><i class="fas fa-exclamation-circle text-danger"></i> Réapprovisionner {{ produit[1] }}</li>
                            {% endfor %}
                            {% if not stocks_faibles %}
                            <li class="text-success"><i class="fas fa-check-circle"></i> Aucune action urgente</li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-warning">Surveillance</h6>
                        <ul class="list-unstyled">
                            {% for produit in produits %}
                                {% if produit[4] >= 100 and produit[4] < 300 %}
                                <li><i class="fas fa-eye text-warning"></i> Surveiller {{ produit[1] }}</li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-info">Optimisation</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-chart-line text-info"></i> Analyser la rotation des stocks</li>
                            <li><i class="fas fa-calculator text-info"></i> Optimiser les niveaux de réapprovisionnement</li>
                            <li><i class="fas fa-sync text-info"></i> Réviser les prix selon les marges</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<script>
// Fonction pour exporter en CSV
function exporterCSV(tableId, filename) {
    const table = document.getElementById(tableId);
    if (!table) return;

    let csv = [];
    const rows = table.querySelectorAll('tr');

    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = [];
        cols.forEach(col => {
            // Nettoyer le texte (enlever les badges, etc.)
            let text = col.textContent.replace(/\s+/g, ' ').trim();
            rowData.push('"' + text.replace(/"/g, '""') + '"');
        });
        csv.push(rowData.join(','));
    });

    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// DataTable
$(document).ready(function() {
    $('#stocksTable').DataTable({
        "language": {
            "search": "Rechercher:",
            "lengthMenu": "Afficher _MENU_ entrées",
            "info": "Affichage de _START_ à _END_ sur _TOTAL_ entrées",
            "paginate": {
                "first": "Premier",
                "last": "Dernier",
                "next": "Suivant",
                "previous": "Précédent"
            }
        },
        "pageLength": 25,
        "order": [[ 1, "asc" ]],
        "columnDefs": [
            { "type": "num", "targets": [1, 2, 3, 4, 5] }
        ]
    });
});
</script>
{% endblock %}
