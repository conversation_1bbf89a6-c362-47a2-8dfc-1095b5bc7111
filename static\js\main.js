// JavaScript principal pour le système de comptabilité

// Fonction pour formater les montants
function formatMontant(montant) {
    return new Intl.NumberFormat('fr-FR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(montant);
}

// Fonction pour formater les dates
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
}

// Fonction pour confirmer les suppressions
function confirmerSuppression(message = 'Êtes-vous sûr de vouloir supprimer cet élément ?') {
    return confirm(message);
}

// Fonction pour afficher les notifications
function afficherNotification(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.main-content');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss après 5 secondes
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Fonction pour valider les formulaires
function validerFormulaire(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const inputs = form.querySelectorAll('input[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// Fonction pour réinitialiser les formulaires
function reinitialiserFormulaire(formId) {
    const form = document.getElementById(formId);
    if (form) {
        form.reset();
        form.querySelectorAll('.is-invalid').forEach(input => {
            input.classList.remove('is-invalid');
        });
    }
}

// Fonction pour gérer les modals
function ouvrirModal(modalId) {
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();
}

function fermerModal(modalId) {
    const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
    if (modal) {
        modal.hide();
    }
}

// Fonction pour exporter les données en CSV
function exporterCSV(tableId, filename = 'export.csv') {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    let csv = [];
    const rows = table.querySelectorAll('tr');
    
    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = [];
        cols.forEach(col => {
            rowData.push('"' + col.textContent.replace(/"/g, '""') + '"');
        });
        csv.push(rowData.join(','));
    });
    
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Fonction pour imprimer une section
function imprimerSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (!section) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>Impression</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                @media print {
                    .no-print { display: none !important; }
                }
            </style>
        </head>
        <body>
            ${section.innerHTML}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// Fonction pour calculer automatiquement les totaux
function calculerTotal(quantiteId, prixId, totalId) {
    const quantite = parseFloat(document.getElementById(quantiteId).value) || 0;
    const prix = parseFloat(document.getElementById(prixId).value) || 0;
    const total = quantite * prix;
    
    document.getElementById(totalId).value = total.toFixed(2);
    return total;
}

// Fonction pour valider les emails
function validerEmail(email) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}

// Fonction pour valider les numéros de téléphone marocains
function validerTelephoneMaroc(telephone) {
    const regex = /^(\+212|0)[5-7][0-9]{8}$/;
    return regex.test(telephone.replace(/\s/g, ''));
}

// Fonction pour valider les CIN marocains
function validerCIN(cin) {
    const regex = /^[A-Z]{1,2}[0-9]{1,6}$/;
    return regex.test(cin.toUpperCase());
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les tooltips Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialiser les popovers Bootstrap
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Ajouter la classe active au lien de navigation actuel
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
    
    // Gérer les formulaires avec validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredInputs = form.querySelectorAll('input[required], select[required]');
            let isValid = true;
            
            requiredInputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                afficherNotification('Veuillez remplir tous les champs obligatoires.', 'danger');
            }
        });
    });
});

// Fonction pour gérer le menu mobile
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('show');
}

// Fermer le sidebar mobile en cliquant à l'extérieur
document.addEventListener('click', function(e) {
    const sidebar = document.querySelector('.sidebar');
    const toggleBtn = document.querySelector('.sidebar-toggle');
    
    if (sidebar && sidebar.classList.contains('show') && 
        !sidebar.contains(e.target) && 
        !toggleBtn?.contains(e.target)) {
        sidebar.classList.remove('show');
    }
});

// Fonction pour sauvegarder les préférences utilisateur
function sauvegarderPreferences(key, value) {
    localStorage.setItem(`compta_${key}`, JSON.stringify(value));
}

function chargerPreferences(key, defaultValue = null) {
    const stored = localStorage.getItem(`compta_${key}`);
    return stored ? JSON.parse(stored) : defaultValue;
}

// Fonction pour gérer les raccourcis clavier
document.addEventListener('keydown', function(e) {
    // Ctrl+S pour sauvegarder (empêcher le comportement par défaut)
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        const submitBtn = document.querySelector('button[type="submit"]:not([disabled])');
        if (submitBtn) {
            submitBtn.click();
        }
    }
    
    // Échap pour fermer les modals
    if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
            const modal = bootstrap.Modal.getInstance(openModal);
            if (modal) {
                modal.hide();
            }
        }
    }
});

// Export des fonctions pour utilisation globale
window.ComptaApp = {
    formatMontant,
    formatDate,
    confirmerSuppression,
    afficherNotification,
    validerFormulaire,
    reinitialiserFormulaire,
    ouvrirModal,
    fermerModal,
    exporterCSV,
    imprimerSection,
    calculerTotal,
    validerEmail,
    validerTelephoneMaroc,
    validerCIN,
    sauvegarderPreferences,
    chargerPreferences
};
