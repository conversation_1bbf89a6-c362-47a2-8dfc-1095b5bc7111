"""
Modèle Transaction pour la gestion des achats et ventes de carburants
"""

from models.database import db
from models.produit import Produit
from models.client import Client
from models.fournisseur import Fournisseur
from datetime import datetime

class Transaction:
    def __init__(self, type_transaction, type_entite, entite_id, produit_id, quantite, prix_unitaire):
        self.type = type_transaction  # 'achat' ou 'vente'
        self.type_entite = type_entite  # 'client' ou 'fournisseur'
        self.entite_id = entite_id
        self.produit_id = produit_id
        self.quantite = quantite
        self.prix_unitaire = prix_unitaire
        self.total = quantite * prix_unitaire
    
    def save(self):
        """Sauvegarder la transaction et mettre à jour les stocks/soldes"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # Enregistrer la transaction
        cursor.execute('''
            INSERT INTO transactions (type, type_entite, entite_id, produit_id, quantite, prix_unitaire, total)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (self.type, self.type_entite, self.entite_id, self.produit_id, 
              self.quantite, self.prix_unitaire, self.total))
        
        transaction_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        # Mettre à jour les stocks et soldes
        self._update_stock_and_balance()
        
        return transaction_id
    
    def _update_stock_and_balance(self):
        """Mettre à jour les stocks de produits et les soldes des entités"""
        if self.type == 'achat':
            # Achat : augmenter le stock, diminuer le solde fournisseur
            Produit.ajouter_stock(self.produit_id, self.quantite)
            if self.type_entite == 'fournisseur':
                fournisseur = Fournisseur.get_by_id(self.entite_id)
                if fournisseur:
                    nouveau_solde = fournisseur[4] - self.total  # solde - montant acheté
                    Fournisseur.update_solde(self.entite_id, nouveau_solde)
        
        elif self.type == 'vente':
            # Vente : diminuer le stock, augmenter le solde client
            Produit.retirer_stock(self.produit_id, self.quantite)
            if self.type_entite == 'client':
                client = Client.get_by_id(self.entite_id)
                if client:
                    nouveau_solde = client[4] + self.total  # solde + montant vendu
                    Client.update_solde(self.entite_id, nouveau_solde)
    
    @staticmethod
    def get_all():
        """Récupérer toutes les transactions"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT t.*, p.nom as produit_nom
            FROM transactions t
            JOIN produits p ON t.produit_id = p.id
            ORDER BY t.date_transaction DESC
        ''')
        transactions = cursor.fetchall()
        conn.close()
        
        return transactions
    
    @staticmethod
    def get_by_period(date_debut, date_fin):
        """Récupérer les transactions par période"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT t.*, p.nom as produit_nom
            FROM transactions t
            JOIN produits p ON t.produit_id = p.id
            WHERE DATE(t.date_transaction) BETWEEN ? AND ?
            ORDER BY t.date_transaction DESC
        ''', (date_debut, date_fin))
        transactions = cursor.fetchall()
        conn.close()
        
        return transactions
    
    @staticmethod
    def get_ventes_mensuelles(mois, annee):
        """Récupérer les ventes d'un mois donné"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT t.*, p.nom as produit_nom
            FROM transactions t
            JOIN produits p ON t.produit_id = p.id
            WHERE t.type = 'vente' 
            AND strftime('%m', t.date_transaction) = ? 
            AND strftime('%Y', t.date_transaction) = ?
            ORDER BY t.date_transaction DESC
        ''', (f"{mois:02d}", str(annee)))
        ventes = cursor.fetchall()
        conn.close()
        
        return ventes
    
    @staticmethod
    def get_achats_mensuels(mois, annee):
        """Récupérer les achats d'un mois donné"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT t.*, p.nom as produit_nom
            FROM transactions t
            JOIN produits p ON t.produit_id = p.id
            WHERE t.type = 'achat' 
            AND strftime('%m', t.date_transaction) = ? 
            AND strftime('%Y', t.date_transaction) = ?
            ORDER BY t.date_transaction DESC
        ''', (f"{mois:02d}", str(annee)))
        achats = cursor.fetchall()
        conn.close()
        
        return achats
    
    @staticmethod
    def calculer_ca_mensuel(mois, annee):
        """Calculer le chiffre d'affaires mensuel"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT SUM(total) as ca_total
            FROM transactions
            WHERE type = 'vente' 
            AND strftime('%m', date_transaction) = ? 
            AND strftime('%Y', date_transaction) = ?
        ''', (f"{mois:02d}", str(annee)))
        
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result[0] else 0.0
