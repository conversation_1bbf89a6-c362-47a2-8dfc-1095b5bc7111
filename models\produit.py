"""
Modèle Produit pour la gestion des carburants de l'entreprise
"""

from models.database import db

class Produit:
    def __init__(self, nom, prix_achat, prix_vente, quantite=0.0):
        self.nom = nom
        self.prix_achat = prix_achat
        self.prix_vente = prix_vente
        self.quantite = quantite
    
    def save(self):
        """Sauvegarder le produit en base de données"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO produits (nom, prix_achat, prix_vente, quantite)
            VALUES (?, ?, ?, ?)
        ''', (self.nom, self.prix_achat, self.prix_vente, self.quantite))
        
        produit_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return produit_id
    
    @staticmethod
    def get_all():
        """Récupérer tous les produits"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM produits ORDER BY nom')
        produits = cursor.fetchall()
        conn.close()
        
        return produits
    
    @staticmethod
    def get_by_id(produit_id):
        """Récupérer un produit par son ID"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM produits WHERE id = ?', (produit_id,))
        produit = cursor.fetchone()
        conn.close()
        
        return produit
    
    @staticmethod
    def update(produit_id, nom, prix_achat, prix_vente, quantite):
        """Mettre à jour un produit"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE produits 
            SET nom = ?, prix_achat = ?, prix_vente = ?, quantite = ?
            WHERE id = ?
        ''', (nom, prix_achat, prix_vente, quantite, produit_id))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def update_quantite(produit_id, nouvelle_quantite):
        """Mettre à jour la quantité d'un produit"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('UPDATE produits SET quantite = ? WHERE id = ?', (nouvelle_quantite, produit_id))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def ajouter_stock(produit_id, quantite_ajoutee):
        """Ajouter du stock à un produit"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('UPDATE produits SET quantite = quantite + ? WHERE id = ?', (quantite_ajoutee, produit_id))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def retirer_stock(produit_id, quantite_retiree):
        """Retirer du stock d'un produit"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('UPDATE produits SET quantite = quantite - ? WHERE id = ?', (quantite_retiree, produit_id))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def delete(produit_id):
        """Supprimer un produit"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM produits WHERE id = ?', (produit_id,))
        
        conn.commit()
        conn.close()
    
    @staticmethod
    def get_stock_faible(seuil=100):
        """Récupérer les produits avec un stock faible"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM produits WHERE quantite < ? ORDER BY quantite ASC', (seuil,))
        produits = cursor.fetchall()
        conn.close()
        
        return produits
