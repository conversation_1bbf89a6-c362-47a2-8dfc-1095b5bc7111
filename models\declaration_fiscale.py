"""
Modèle Déclaration Fiscale pour la gestion des déclarations d'impôts
"""

from models.database import db
from datetime import datetime

class DeclarationFiscale:
    def __init__(self, type_impot, periode, montant):
        self.type_impot = type_impot  # 'TVA', 'IS', 'IR'
        self.periode = periode  # Format: 'YYYY-MM' pour mensuel, 'YYYY' pour annuel
        self.montant = montant
    
    def save(self):
        """Sauvegarder la déclaration fiscale"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO declarations_fiscales (type_impot, periode, montant)
            VALUES (?, ?, ?)
        ''', (self.type_impot, self.periode, self.montant))
        
        declaration_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return declaration_id
    
    @staticmethod
    def get_all():
        """Récupérer toutes les déclarations fiscales"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM declarations_fiscales ORDER BY date_declaration DESC')
        declarations = cursor.fetchall()
        conn.close()
        
        return declarations
    
    @staticmethod
    def get_by_type(type_impot):
        """Récupérer les déclarations par type d'impôt"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM declarations_fiscales 
            WHERE type_impot = ? 
            ORDER BY periode DESC
        ''', (type_impot,))
        declarations = cursor.fetchall()
        conn.close()
        
        return declarations
    
    @staticmethod
    def get_by_periode(periode):
        """Récupérer les déclarations par période"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM declarations_fiscales 
            WHERE periode = ? 
            ORDER BY type_impot
        ''', (periode,))
        declarations = cursor.fetchall()
        conn.close()
        
        return declarations
    
    @staticmethod
    def declarer_tva_mensuelle(mois, annee, tva_collectee, tva_deductible):
        """Créer une déclaration TVA mensuelle"""
        periode = f"{annee}-{mois:02d}"
        tva_due = tva_collectee - tva_deductible
        
        declaration = DeclarationFiscale('TVA', periode, tva_due)
        return declaration.save()
    
    @staticmethod
    def declarer_is_annuel(annee, montant_is):
        """Créer une déclaration IS annuelle"""
        periode = str(annee)
        
        declaration = DeclarationFiscale('IS', periode, montant_is)
        return declaration.save()
    
    @staticmethod
    def declarer_ir_mensuel(mois, annee, montant_ir):
        """Créer une déclaration IR mensuelle"""
        periode = f"{annee}-{mois:02d}"
        
        declaration = DeclarationFiscale('IR', periode, montant_ir)
        return declaration.save()
    
    @staticmethod
    def get_total_impots_annee(annee):
        """Calculer le total des impôts payés dans l'année"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT type_impot, SUM(montant) as total
            FROM declarations_fiscales 
            WHERE periode LIKE ?
            GROUP BY type_impot
        ''', (f"{annee}%",))
        
        results = cursor.fetchall()
        conn.close()
        
        totaux = {}
        for result in results:
            totaux[result[0]] = result[1]
        
        return totaux
    
    @staticmethod
    def delete(declaration_id):
        """Supprimer une déclaration fiscale"""
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM declarations_fiscales WHERE id = ?', (declaration_id,))
        
        conn.commit()
        conn.close()
