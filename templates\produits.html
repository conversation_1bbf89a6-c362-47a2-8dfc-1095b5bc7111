{% extends "base.html" %}

{% block title %}Gestion des Produits - Système de Comptabilité{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-oil-can"></i> Gestion des Produits</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterProduitModal">
            <i class="fas fa-plus"></i> Ajouter Produit
        </button>
    </div>
</div>

<!-- Alertes stock faible -->
{% set stocks_faibles = [] %}
{% for produit in produits %}
    {% if produit[4] < 100 %}
        {% set _ = stocks_faibles.append(produit) %}
    {% endif %}
{% endfor %}

{% if stocks_faibles %}
<div class="alert alert-warning" role="alert">
    <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Stocks Faibles</h4>
    <p>Les produits suivants ont un stock inférieur à 100 unités :</p>
    <ul class="mb-0">
        {% for produit in stocks_faibles %}
        <li><strong>{{ produit[1] }}</strong> : {{ "{:,.0f}".format(produit[4]) }} unités</li>
        {% endfor %}
    </ul>
</div>
{% endif %}

<!-- Liste des produits -->
<div class="card shadow">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold"><i class="fas fa-list"></i> Liste des Produits</h6>
    </div>
    <div class="card-body">
        {% if produits %}
        <div class="table-responsive">
            <table class="table table-bordered" id="produitsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Prix Achat (DH)</th>
                        <th>Prix Vente (DH)</th>
                        <th>Marge (%)</th>
                        <th>Stock</th>
                        <th>Valeur Stock (DH)</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for produit in produits %}
                    {% set marge = ((produit[3] - produit[2]) / produit[2] * 100) if produit[2] > 0 else 0 %}
                    {% set valeur_stock = produit[4] * produit[2] %}
                    <tr>
                        <td>{{ produit[0] }}</td>
                        <td>{{ produit[1] }}</td>
                        <td>{{ "{:,.2f}".format(produit[2]) }}</td>
                        <td>{{ "{:,.2f}".format(produit[3]) }}</td>
                        <td>
                            <span class="badge bg-{{ 'success' if marge > 20 else 'warning' if marge > 10 else 'danger' }}">
                                {{ "{:,.1f}".format(marge) }}%
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'danger' if produit[4] < 100 else 'warning' if produit[4] < 500 else 'success' }}">
                                {{ "{:,.0f}".format(produit[4]) }}
                            </span>
                        </td>
                        <td>{{ "{:,.2f}".format(valeur_stock) }}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="modifierProduit({{ produit[0] }}, '{{ produit[1] }}', {{ produit[2] }}, {{ produit[3] }}, {{ produit[4] }})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info" onclick="ajusterStock({{ produit[0] }}, '{{ produit[1] }}', {{ produit[4] }})">
                                <i class="fas fa-boxes"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-muted">Aucun produit enregistré.</p>
        {% endif %}
    </div>
</div>

<!-- Modal Ajouter Produit -->
<div class="modal fade" id="ajouterProduitModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Ajouter un Produit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('ajouter_produit') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nom" class="form-label">Nom du Produit *</label>
                        <input type="text" class="form-control" id="nom" name="nom" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="prix_achat" class="form-label">Prix d'Achat (DH) *</label>
                                <input type="number" step="0.01" class="form-control" id="prix_achat" name="prix_achat" required onchange="calculerMarge()">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="prix_vente" class="form-label">Prix de Vente (DH) *</label>
                                <input type="number" step="0.01" class="form-control" id="prix_vente" name="prix_vente" required onchange="calculerMarge()">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantite" class="form-label">Stock Initial</label>
                                <input type="number" step="0.01" class="form-control" id="quantite" name="quantite" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="marge_calc" class="form-label">Marge (%)</label>
                                <input type="text" class="form-control" id="marge_calc" readonly>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Modifier Produit -->
<div class="modal fade" id="modifierProduitModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Modifier le Produit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="modifierProduitForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="modif_nom" class="form-label">Nom du Produit *</label>
                        <input type="text" class="form-control" id="modif_nom" name="nom" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modif_prix_achat" class="form-label">Prix d'Achat (DH) *</label>
                                <input type="number" step="0.01" class="form-control" id="modif_prix_achat" name="prix_achat" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modif_prix_vente" class="form-label">Prix de Vente (DH) *</label>
                                <input type="number" step="0.01" class="form-control" id="modif_prix_vente" name="prix_vente" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="modif_quantite" class="form-label">Stock</label>
                        <input type="number" step="0.01" class="form-control" id="modif_quantite" name="quantite">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-warning">Modifier</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Ajuster Stock -->
<div class="modal fade" id="ajusterStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-boxes"></i> Ajuster le Stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><strong>Produit :</strong> <span id="stock_produit_nom"></span></p>
                <p><strong>Stock actuel :</strong> <span id="stock_actuel"></span> unités</p>
                
                <div class="mb-3">
                    <label for="type_ajustement" class="form-label">Type d'ajustement</label>
                    <select class="form-select" id="type_ajustement">
                        <option value="ajouter">Ajouter au stock</option>
                        <option value="retirer">Retirer du stock</option>
                        <option value="corriger">Corriger le stock</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="quantite_ajustement" class="form-label">Quantité</label>
                    <input type="number" step="0.01" class="form-control" id="quantite_ajustement" required>
                </div>
                
                <div class="mb-3">
                    <label for="nouveau_stock" class="form-label">Nouveau stock</label>
                    <input type="number" step="0.01" class="form-control" id="nouveau_stock" readonly>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="confirmerAjustement()">Confirmer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<script>
function calculerMarge() {
    const prixAchat = parseFloat(document.getElementById('prix_achat').value) || 0;
    const prixVente = parseFloat(document.getElementById('prix_vente').value) || 0;
    
    if (prixAchat > 0) {
        const marge = ((prixVente - prixAchat) / prixAchat * 100);
        document.getElementById('marge_calc').value = marge.toFixed(1) + '%';
    } else {
        document.getElementById('marge_calc').value = '';
    }
}

function modifierProduit(id, nom, prixAchat, prixVente, quantite) {
    document.getElementById('modif_nom').value = nom;
    document.getElementById('modif_prix_achat').value = prixAchat;
    document.getElementById('modif_prix_vente').value = prixVente;
    document.getElementById('modif_quantite').value = quantite;
    
    document.getElementById('modifierProduitForm').action = '/produits/modifier/' + id;
    
    var modal = new bootstrap.Modal(document.getElementById('modifierProduitModal'));
    modal.show();
}

let stockProduitId = null;
let stockActuelValue = 0;

function ajusterStock(id, nom, stockActuel) {
    stockProduitId = id;
    stockActuelValue = stockActuel;
    
    document.getElementById('stock_produit_nom').textContent = nom;
    document.getElementById('stock_actuel').textContent = stockActuel.toFixed(0);
    document.getElementById('quantite_ajustement').value = '';
    document.getElementById('nouveau_stock').value = stockActuel.toFixed(2);
    
    var modal = new bootstrap.Modal(document.getElementById('ajusterStockModal'));
    modal.show();
}

// Calculer le nouveau stock en temps réel
document.getElementById('quantite_ajustement').addEventListener('input', function() {
    const type = document.getElementById('type_ajustement').value;
    const quantite = parseFloat(this.value) || 0;
    let nouveauStock = stockActuelValue;
    
    if (type === 'ajouter') {
        nouveauStock = stockActuelValue + quantite;
    } else if (type === 'retirer') {
        nouveauStock = stockActuelValue - quantite;
    } else if (type === 'corriger') {
        nouveauStock = quantite;
    }
    
    document.getElementById('nouveau_stock').value = nouveauStock.toFixed(2);
});

document.getElementById('type_ajustement').addEventListener('change', function() {
    document.getElementById('quantite_ajustement').dispatchEvent(new Event('input'));
});

function confirmerAjustement() {
    const nouveauStock = parseFloat(document.getElementById('nouveau_stock').value);
    
    // Ici vous pourriez faire un appel AJAX pour mettre à jour le stock
    // Pour l'instant, on recharge la page
    alert('Fonctionnalité à implémenter : mise à jour du stock via AJAX');
}

// DataTable
$(document).ready(function() {
    $('#produitsTable').DataTable({
        "language": {
            "search": "Rechercher:",
            "lengthMenu": "Afficher _MENU_ entrées",
            "info": "Affichage de _START_ à _END_ sur _TOTAL_ entrées",
            "paginate": {
                "first": "Premier",
                "last": "Dernier",
                "next": "Suivant",
                "previous": "Précédent"
            }
        },
        "pageLength": 25,
        "order": [[ 1, "asc" ]]
    });
});
</script>
{% endblock %}
