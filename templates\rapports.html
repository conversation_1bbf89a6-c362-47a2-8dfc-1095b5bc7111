{% extends "base.html" %}

{% block title %}Rapports - Système de Comptabilité{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-chart-bar"></i> Rapports et Analyses</h1>
</div>

<!-- Sélecteurs de période -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-calendar"></i> Rapport Mensuel</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="mois_rapport" class="form-label">Mois</label>
                        <select class="form-select" id="mois_rapport">
                            <option value="1">Janvier</option>
                            <option value="2">Février</option>
                            <option value="3">Mars</option>
                            <option value="4">Avril</option>
                            <option value="5">Mai</option>
                            <option value="6">Juin</option>
                            <option value="7">Juillet</option>
                            <option value="8">Août</option>
                            <option value="9">Septembre</option>
                            <option value="10">Octobre</option>
                            <option value="11">Novembre</option>
                            <option value="12">Décembre</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="annee_rapport" class="form-label">Année</label>
                        <select class="form-select" id="annee_rapport">
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="genererRapportMensuel()">
                        <i class="fas fa-chart-line"></i> Générer Rapport Mensuel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-calendar-alt"></i> Bilan Annuel</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="annee_bilan" class="form-label">Année</label>
                        <select class="form-select" id="annee_bilan">
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-success" onclick="genererBilanAnnuel()">
                        <i class="fas fa-chart-pie"></i> Générer Bilan Annuel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Zone d'affichage des rapports -->
<div id="rapportContainer" style="display: none;">
    <div class="card shadow">
        <div class="card-header">
            <h6 class="mb-0" id="rapportTitre"><i class="fas fa-file-alt"></i> Rapport</h6>
        </div>
        <div class="card-body">
            <div id="rapportContenu">
                <!-- Le contenu sera injecté ici -->
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-secondary" onclick="imprimerRapport()">
                    <i class="fas fa-print"></i> Imprimer
                </button>
                <button class="btn btn-info" onclick="exporterRapport()">
                    <i class="fas fa-download"></i> Exporter
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Rapports rapides -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-boxes"></i> État des Stocks</h6>
            </div>
            <div class="card-body">
                <p class="card-text">Visualiser l'état actuel des stocks et les alertes.</p>
                <button class="btn btn-outline-primary me-2" onclick="genererRapportStocks()">
                    <i class="fas fa-chart-bar"></i> Rapport Rapide
                </button>
                <a href="{{ url_for('rapport_stocks_detail') }}" class="btn btn-primary">
                    <i class="fas fa-eye"></i> Vue Détaillée
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-users"></i> Créances Clients</h6>
            </div>
            <div class="card-body">
                <p class="card-text">Liste des créances clients en cours.</p>
                <button class="btn btn-outline-warning" onclick="genererRapportCreances()">
                    Voir les Créances
                </button>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-truck"></i> Dettes Fournisseurs</h6>
            </div>
            <div class="card-body">
                <p class="card-text">Liste des dettes envers les fournisseurs.</p>
                <button class="btn btn-outline-danger" onclick="genererRapportDettes()">
                    Voir les Dettes
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Déclarations fiscales -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-file-invoice-dollar"></i> Déclarations Fiscales</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>TVA Mensuelle</h6>
                        <div class="input-group mb-3">
                            <select class="form-select" id="mois_tva">
                                <option value="1">Janvier</option>
                                <option value="2">Février</option>
                                <option value="3">Mars</option>
                                <option value="4">Avril</option>
                                <option value="5">Mai</option>
                                <option value="6">Juin</option>
                                <option value="7">Juillet</option>
                                <option value="8">Août</option>
                                <option value="9">Septembre</option>
                                <option value="10">Octobre</option>
                                <option value="11">Novembre</option>
                                <option value="12">Décembre</option>
                            </select>
                            <select class="form-select" id="annee_tva">
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                            </select>
                        </div>
                        <button class="btn btn-primary btn-sm" onclick="genererDeclarationTVA()">
                            Générer TVA
                        </button>
                    </div>

                    <div class="col-md-4">
                        <h6>IR Mensuel</h6>
                        <div class="input-group mb-3">
                            <select class="form-select" id="mois_ir">
                                <option value="1">Janvier</option>
                                <option value="2">Février</option>
                                <option value="3">Mars</option>
                                <option value="4">Avril</option>
                                <option value="5">Mai</option>
                                <option value="6">Juin</option>
                                <option value="7">Juillet</option>
                                <option value="8">Août</option>
                                <option value="9">Septembre</option>
                                <option value="10">Octobre</option>
                                <option value="11">Novembre</option>
                                <option value="12">Décembre</option>
                            </select>
                            <select class="form-select" id="annee_ir">
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                            </select>
                        </div>
                        <button class="btn btn-warning btn-sm" onclick="genererDeclarationIR()">
                            Générer IR
                        </button>
                    </div>

                    <div class="col-md-4">
                        <h6>IS Annuel</h6>
                        <div class="input-group mb-3">
                            <select class="form-select" id="annee_is">
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                                <option value="2022">2022</option>
                            </select>
                        </div>
                        <button class="btn btn-success btn-sm" onclick="genererDeclarationIS()">
                            Générer IS
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialiser les sélecteurs avec le mois et l'année actuels
document.addEventListener('DOMContentLoaded', function() {
    const maintenant = new Date();
    const moisActuel = maintenant.getMonth() + 1;
    const anneeActuelle = maintenant.getFullYear();

    document.getElementById('mois_rapport').value = moisActuel;
    document.getElementById('annee_rapport').value = anneeActuelle;
    document.getElementById('annee_bilan').value = anneeActuelle;
    document.getElementById('mois_tva').value = moisActuel;
    document.getElementById('annee_tva').value = anneeActuelle;
    document.getElementById('mois_ir').value = moisActuel;
    document.getElementById('annee_ir').value = anneeActuelle;
    document.getElementById('annee_is').value = anneeActuelle;
});

function genererRapportMensuel() {
    const mois = document.getElementById('mois_rapport').value;
    const annee = document.getElementById('annee_rapport').value;

    fetch(`/rapports/mensuel?mois=${mois}&annee=${annee}`)
        .then(response => response.json())
        .then(data => {
            afficherRapportMensuel(data);
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la génération du rapport');
        });
}

function genererBilanAnnuel() {
    const annee = document.getElementById('annee_bilan').value;

    fetch(`/rapports/annuel?annee=${annee}`)
        .then(response => response.json())
        .then(data => {
            afficherBilanAnnuel(data);
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la génération du bilan');
        });
}

function afficherRapportMensuel(data) {
    const titre = `Rapport Mensuel - ${data.periode}`;
    document.getElementById('rapportTitre').innerHTML = `<i class="fas fa-file-alt"></i> ${titre}`;

    const contenu = `
        <div class="row">
            <div class="col-md-6">
                <h5>Chiffre d'Affaires</h5>
                <table class="table table-sm">
                    <tr><td>CA Mensuel:</td><td><strong>${formatMontant(data.chiffre_affaires.ca_mensuel)} DH</strong></td></tr>
                    <tr><td>Nombre de ventes:</td><td>${data.chiffre_affaires.nombre_ventes}</td></tr>
                    <tr><td>Total achats:</td><td>${formatMontant(data.chiffre_affaires.total_achats)} DH</td></tr>
                    <tr><td>Marge brute:</td><td><strong>${formatMontant(data.chiffre_affaires.marge_brute)} DH</strong></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>TVA</h5>
                <table class="table table-sm">
                    <tr><td>TVA Collectée:</td><td>${formatMontant(data.tva.tva_collectee)} DH</td></tr>
                    <tr><td>TVA Déductible:</td><td>${formatMontant(data.tva.tva_deductible)} DH</td></tr>
                    <tr><td><strong>TVA Due:</strong></td><td><strong>${formatMontant(data.tva.tva_due)} DH</strong></td></tr>
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-6">
                <h5>Masse Salariale</h5>
                <table class="table table-sm">
                    <tr><td>Total Brut:</td><td>${formatMontant(data.masse_salariale.total_brut)} DH</td></tr>
                    <tr><td>Total Net:</td><td><strong>${formatMontant(data.masse_salariale.total_net)} DH</strong></td></tr>
                    <tr><td>Total CNSS:</td><td>${formatMontant(data.masse_salariale.total_cnss)} DH</td></tr>
                    <tr><td>Total IR:</td><td>${formatMontant(data.masse_salariale.total_ir)} DH</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>Créances et Dettes</h5>
                <table class="table table-sm">
                    <tr><td>Créances clients:</td><td>${formatMontant(data.creances_clients.total)} DH</td></tr>
                    <tr><td>Nombre de clients:</td><td>${data.creances_clients.nombre}</td></tr>
                    <tr><td>Dettes fournisseurs:</td><td>${formatMontant(data.dettes_fournisseurs.total)} DH</td></tr>
                    <tr><td>Nombre de fournisseurs:</td><td>${data.dettes_fournisseurs.nombre}</td></tr>
                </table>
            </div>
        </div>
    `;

    document.getElementById('rapportContenu').innerHTML = contenu;
    document.getElementById('rapportContainer').style.display = 'block';
}

function afficherBilanAnnuel(data) {
    const titre = `Bilan Annuel - ${data.annee}`;
    document.getElementById('rapportTitre').innerHTML = `<i class="fas fa-file-alt"></i> ${titre}`;

    const contenu = `
        <div class="row">
            <div class="col-md-6">
                <h5>Résultats Financiers</h5>
                <table class="table table-sm">
                    <tr><td>Chiffre d'Affaires:</td><td><strong>${formatMontant(data.chiffre_affaires_annuel)} DH</strong></td></tr>
                    <tr><td>Total Achats:</td><td>${formatMontant(data.total_achats_annuel)} DH</td></tr>
                    <tr><td>Résultat Brut:</td><td><strong>${formatMontant(data.resultat_brut)} DH</strong></td></tr>
                    <tr><td>Charges Salariales:</td><td>${formatMontant(data.charges_salariales)} DH</td></tr>
                    <tr><td>Résultat avant Impôts:</td><td><strong>${formatMontant(data.resultat_avant_impots)} DH</strong></td></tr>
                    <tr><td>Total Impôts:</td><td>${formatMontant(data.total_impots)} DH</td></tr>
                    <tr><td><strong>Résultat Net:</strong></td><td><strong>${formatMontant(data.resultat_net)} DH</strong></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>Ratios de Performance</h5>
                <table class="table table-sm">
                    <tr><td>Marge Brute:</td><td><strong>${data.marge_brute_pct.toFixed(1)}%</strong></td></tr>
                    <tr><td>Marge Nette:</td><td><strong>${data.marge_nette_pct.toFixed(1)}%</strong></td></tr>
                </table>

                <h6 class="mt-3">Impôts Payés</h6>
                <table class="table table-sm">
                    ${Object.entries(data.impots_payes).map(([type, montant]) =>
                        `<tr><td>${type}:</td><td>${formatMontant(montant)} DH</td></tr>`
                    ).join('')}
                </table>
            </div>
        </div>
    `;

    document.getElementById('rapportContenu').innerHTML = contenu;
    document.getElementById('rapportContainer').style.display = 'block';
}

function formatMontant(montant) {
    return new Intl.NumberFormat('fr-FR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(montant);
}

function genererRapportStocks() {
    fetch('/rapports/stocks')
        .then(response => response.json())
        .then(data => {
            afficherRapportStocks(data);
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la génération du rapport des stocks');
        });
}

function genererRapportCreances() {
    fetch('/rapports/creances')
        .then(response => response.json())
        .then(data => {
            afficherRapportCreances(data);
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la génération du rapport des créances');
        });
}

function genererRapportDettes() {
    fetch('/rapports/dettes')
        .then(response => response.json())
        .then(data => {
            afficherRapportDettes(data);
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la génération du rapport des dettes');
        });
}

function genererDeclarationTVA() {
    const mois = document.getElementById('mois_tva').value;
    const annee = document.getElementById('annee_tva').value;
    alert(`Déclaration TVA ${mois}/${annee} - Fonctionnalité à implémenter`);
}

function genererDeclarationIR() {
    const mois = document.getElementById('mois_ir').value;
    const annee = document.getElementById('annee_ir').value;
    alert(`Déclaration IR ${mois}/${annee} - Fonctionnalité à implémenter`);
}

function genererDeclarationIS() {
    const annee = document.getElementById('annee_is').value;
    alert(`Déclaration IS ${annee} - Fonctionnalité à implémenter`);
}

function imprimerRapport() {
    window.print();
}

function afficherRapportStocks(data) {
    const titre = 'Rapport des Stocks';
    document.getElementById('rapportTitre').innerHTML = `<i class="fas fa-boxes"></i> ${titre}`;

    let stocksFaiblesHtml = '';
    if (data.stocks_faibles.nombre > 0) {
        stocksFaiblesHtml = `
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> Alertes Stock Faible</h6>
                <table class="table table-sm mb-0">
                    <thead><tr><th>Produit</th><th>Stock Actuel</th><th>Valeur</th></tr></thead>
                    <tbody>
                        ${data.stocks_faibles.produits.map(p => `
                            <tr>
                                <td>${p[1]}</td>
                                <td><span class="badge bg-danger">${formatMontant(p[4])}</span></td>
                                <td>${formatMontant(p[4] * p[2])} DH</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    const contenu = `
        ${stocksFaiblesHtml}
        <div class="row">
            <div class="col-md-6">
                <h5>Résumé des Stocks</h5>
                <table class="table table-sm">
                    <tr><td>Nombre de produits:</td><td><strong>${data.nombre_produits}</strong></td></tr>
                    <tr><td>Valeur totale du stock:</td><td><strong>${formatMontant(data.valeur_stock_total)} DH</strong></td></tr>
                    <tr><td>Produits en stock faible:</td><td><span class="badge bg-${data.stocks_faibles.nombre > 0 ? 'danger' : 'success'}">${data.stocks_faibles.nombre}</span></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>Détail par Produit</h5>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr><th>Produit</th><th>Stock</th><th>Prix Achat</th><th>Valeur</th></tr>
                        </thead>
                        <tbody>
                            ${data.detail_stocks.map(p => `
                                <tr>
                                    <td>${p[1]}</td>
                                    <td><span class="badge bg-${p[4] < 100 ? 'danger' : p[4] < 500 ? 'warning' : 'success'}">${formatMontant(p[4])}</span></td>
                                    <td>${formatMontant(p[2])} DH</td>
                                    <td>${formatMontant(p[4] * p[2])} DH</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    document.getElementById('rapportContenu').innerHTML = contenu;
    document.getElementById('rapportContainer').style.display = 'block';
}

function afficherRapportCreances(data) {
    const titre = 'Rapport des Créances Clients';
    document.getElementById('rapportTitre').innerHTML = `<i class="fas fa-users"></i> ${titre}`;

    const contenu = `
        <div class="row">
            <div class="col-md-6">
                <h5>Résumé des Créances</h5>
                <table class="table table-sm">
                    <tr><td>Nombre total de clients:</td><td><strong>${data.nombre_clients_total}</strong></td></tr>
                    <tr><td>Clients avec créances:</td><td><strong>${data.nombre_clients_creances}</strong></td></tr>
                    <tr><td>Total des créances:</td><td><strong>${formatMontant(data.total_creances)} DH</strong></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>Clients avec Créances</h5>
                ${data.nombre_clients_creances > 0 ? `
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr><th>Client</th><th>Téléphone</th><th>Créance</th></tr>
                            </thead>
                            <tbody>
                                ${data.clients_avec_creances.map(c => `
                                    <tr>
                                        <td>${c[1]}</td>
                                        <td>${c[2] || 'N/A'}</td>
                                        <td><span class="badge bg-warning">${formatMontant(c[4])} DH</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : '<p class="text-success">Aucune créance en cours</p>'}
            </div>
        </div>
    `;

    document.getElementById('rapportContenu').innerHTML = contenu;
    document.getElementById('rapportContainer').style.display = 'block';
}

function afficherRapportDettes(data) {
    const titre = 'Rapport des Dettes Fournisseurs';
    document.getElementById('rapportTitre').innerHTML = `<i class="fas fa-truck"></i> ${titre}`;

    const contenu = `
        <div class="row">
            <div class="col-md-6">
                <h5>Résumé des Dettes</h5>
                <table class="table table-sm">
                    <tr><td>Nombre total de fournisseurs:</td><td><strong>${data.nombre_fournisseurs_total}</strong></td></tr>
                    <tr><td>Fournisseurs avec dettes:</td><td><strong>${data.nombre_fournisseurs_dettes}</strong></td></tr>
                    <tr><td>Total des dettes:</td><td><strong>${formatMontant(data.total_dettes)} DH</strong></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>Fournisseurs avec Dettes</h5>
                ${data.nombre_fournisseurs_dettes > 0 ? `
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr><th>Fournisseur</th><th>Téléphone</th><th>Dette</th></tr>
                            </thead>
                            <tbody>
                                ${data.fournisseurs_avec_dettes.map(f => `
                                    <tr>
                                        <td>${f[1]}</td>
                                        <td>${f[2] || 'N/A'}</td>
                                        <td><span class="badge bg-danger">${formatMontant(Math.abs(f[4]))} DH</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : '<p class="text-success">Aucune dette en cours</p>'}
            </div>
        </div>
    `;

    document.getElementById('rapportContenu').innerHTML = contenu;
    document.getElementById('rapportContainer').style.display = 'block';
}

function exporterRapport() {
    alert('Export en PDF - Fonctionnalité à implémenter');
}
</script>
{% endblock %}
